import React from 'react';
import { Database, Cloud, BarChart3, ShoppingCart, CreditCard, MessageSquare, Monitor, Server, Table } from 'lucide-react';
import { StatsCard, FeatureCard } from '../../components/ui/custom-card';

export const metadata = {
  title: 'OfImpact - Business Intelligence & Analytics Platform | ofstartup.ai',
  description: 'Transform your data into actionable insights with OfImpact, our comprehensive AI-powered business intelligence platform. Real-time dashboards, predictive analytics, and seamless integrations.',
  keywords: [
    'OfImpact',
    'business intelligence',
    'data analytics',
    'AI analytics',
    'real-time dashboards',
    'predictive analytics',
    'data visualization',
    'business insights',
    'data transformation',
    'analytics platform'
  ],
  openGraph: {
    title: 'OfImpact - AI-Powered Business Intelligence | ofstartup.ai',
    description: 'Transform your data into actionable insights with OfImpact. Real-time dashboards, predictive analytics, and AI-powered business intelligence.',
    type: 'website',
    locale: 'en_US',
    url: 'https://ofstartupglobal.com/ofimpact',
    siteName: 'ofstartup.ai',
    images: [
      {
        url: '/api/og?title=OfImpact%20-%20AI-Powered%20Business%20Intelligence&description=Transform%20your%20data%20into%20actionable%20insights%20with%20real-time%20dashboards&page=ofimpact',
        width: 1200,
        height: 630,
        alt: 'OfImpact - Business Intelligence Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'OfImpact - AI-Powered Business Intelligence | ofstartup.ai',
    description: 'Transform your data into actionable insights with OfImpact. Real-time dashboards and predictive analytics.',
    images: ['/api/og?title=OfImpact%20-%20AI-Powered%20Business%20Intelligence&description=Transform%20your%20data%20into%20actionable%20insights%20with%20real-time%20dashboards&page=ofimpact'],
  },
  alternates: {
    canonical: 'https://ofstartupglobal.com/ofimpact',
  },
};

// Custom SVG Icons for integrations
const SalesforceIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
  </svg>
);

const HubSpotIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
  </svg>
);

const GoogleAnalyticsIcon = () => (
  <svg className="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
  </svg>
);

export default function OfImpactPage() {
  const features = [
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      title: "Real-time Analytics",
      description: "Monitor your business performance with live data streams and instant insights"
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      title: "AI-Powered Predictions",
      description: "Leverage machine learning to forecast trends and make data-driven decisions"
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      ),
      title: "Custom Dashboards",
      description: "Create personalized dashboards tailored to your specific business needs"
    }
  ];

  return (
    <>
      {/* What is OfImpact Section */}
      <section className="py-36 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col gap-16 items-center justify-center">

            <div className='flex flex-col items-center justify-center'>
              <h2 className="display-medium text-primary mb-6">
                What is OfImpact?
              </h2>
              <p className="headline-medium text-center text-secondary mb-8">
                OfImpact is a comprehensive business intelligence platform that transforms raw data into actionable insights, empowering organizations to make informed decisions and drive sustainable growth.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {features.map((feature, index) => (
                <FeatureCard
                  key={index}
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Cost Savings Section */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Proven Cost Savings
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              See how OfImpact delivers measurable ROI through intelligent automation and data-driven insights.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <StatsCard
              title="Operational Costs"
              value="40%"
              description="Average reduction in operational expenses"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              }
              trend={{ value: 15, isPositive: true }}
            />
            <StatsCard
              title="Decision Speed"
              value="5x"
              description="Faster data-driven decision making"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              trend={{ value: 25, isPositive: true }}
            />
            <StatsCard
              title="Data Accuracy"
              value="99.5%"
              description="Improved data quality and reliability"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              trend={{ value: 8, isPositive: true }}
            />
            <StatsCard
              title="ROI Timeline"
              value="3 months"
              description="Average time to see positive ROI"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              }
              trend={{ value: 20, isPositive: true }}
            />
          </div>
        </div>
      </section>

      {/* Integration Section */}
      <section className="py-24 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Integration Made Simple
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Connect OfImpact with your existing tools and data sources in minutes, not months.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            {[
              { name: "Salesforce", icon: <SalesforceIcon /> },
              { name: "HubSpot", icon: <HubSpotIcon /> },
              { name: "Google Analytics", icon: <GoogleAnalyticsIcon /> },
              { name: "Shopify", icon: <ShoppingCart className="w-8 h-8" /> },
              { name: "Stripe", icon: <CreditCard className="w-8 h-8" /> },
              { name: "Slack", icon: <MessageSquare className="w-8 h-8" /> },
              { name: "Microsoft", icon: <Monitor className="w-8 h-8" /> },
              { name: "AWS", icon: <Cloud className="w-8 h-8" /> },
              { name: "PostgreSQL", icon: <Database className="w-8 h-8" /> },
              { name: "MongoDB", icon: <Server className="w-8 h-8" /> },
              { name: "Tableau", icon: <Table className="w-8 h-8" /> },
              { name: "Power BI", icon: <BarChart3 className="w-8 h-8" /> }
            ].map((integration, index) => (
              <div key={index} className="text-center group">
                <div className="w-16 h-16 mx-auto mb-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center text-purple-600 dark:text-purple-400 border border-purple-500/20 group-hover:border-purple-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20 group-hover:scale-105">
                  {integration.icon}
                </div>
                <p className="body-small text-secondary font-medium">{integration.name}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-purple-600 via-black to-purple-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="display-medium mb-6">
            Ready to Transform Your Data?
          </h2>
          <p className="headline-medium mb-10 opacity-90">
            Connect with us and see how OfImpact can revolutionize your business intelligence.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="btn-primary bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 body-large font-semibold rounded-lg"
            >
              Connect With Us
            </a>
            <a
              href="/portfolio"
              className="btn-primary bg-transparent border-2 border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4 body-large font-semibold rounded-lg"
            >
              View Case Studies
            </a>
          </div>
        </div>
      </section>
    </>
  );
}
