# Ofstartup.ai Website Rebuild Product Requirements Document (PRD)

## Goals and Background Context

### Goals
*   Create a visually stunning, user-friendly, and highly performant website for Ofstartup.ai.
*   Showcase Ofstartup.ai's advanced capabilities in building visually stunning platforms and leveraging cutting-edge AI.
*   Clearly communicate Ofstartup.ai's unique value proposition as a service-based company driven by AI agentic workflows and coding agents.
*   Highlight the benefits of faster delivery, reduced development costs, quick deployment, and superior quality achieved through AI-driven services.
*   Establish Ofstartup.ai as a leader in AI-driven service delivery and innovation.
*   Provide a seamless and engaging user experience with both light and dark theme support.

### Background Context
The existing `ofstartup` website is basic in design and does not adequately reflect the company's advanced capabilities in building visually stunning and AI-powered solutions. The objective of this rebuild is to create a new website that serves as a showcase for `ofstartup`'s expertise, emphasizing its service-based model and the transformative power of its AI agentic workflows and coding agents. The new site will draw inspiration from top-tier SaaS platforms and large service companies, incorporating modern design aesthetics, advanced animations (GSAP), and a focus on clear, persuasive communication of value. The entire design and development process will start from a blank slate, with the intention to replace existing content and components.

### Change Log

| Date         | Version | Description                               | Author |
| :----------- | :------ | :---------------------------------------- | :----- |
| Aug 6, 2025 | 1.0     | Initial draft based on Analyst discussion | John   |
| Aug 6, 2025 | 1.1     | Added Portfolio page requirements         | John   |
| Aug 6, 2025 | 1.2     | Added server/client separation, robots.txt, sitemap.xml requirements | John   |
| Aug 6, 2025 | 1.3     | Removed backend/contact form; added static contact info from config | John   |
| Aug 6, 2025 | 1.4     | Added requirement for shadcn-charts and data visualization | John   |
| Aug 6, 2025 | 1.5     | Added Founder Deck page requirements      | John   |

## Requirements

### Functional
1.  **FR1: Core Page Content Display:** The website shall display content for the Home, About, Services, OfImpact, and OfLogic pages, with each page featuring long-form, descriptive, and persuasive content (minimum 800–1000 words).
2.  **FR2: Static Contact Information Display:** The website shall display static contact information (WhatsApp number, call number, email address, company name, physical address) on the Contact page and/or footer, sourced from a centralized configuration file.
3.  **FR3: Navigation System:** The website shall feature a simple, sticky navigation bar with links to Home, About, Services, OfImpact, OfLogic, Contact, Portfolio, and Founder Deck.
4.  **FR4: Call-to-Action (CTA) Display:** Prominent and strategically placed CTAs (e.g., “Get Started,” “Contact Us,” “Discover Our AI Advantage”) shall be displayed across all pages, directing users to static contact information or relevant service pages.
5.  **FR5: AI-Driven Service Explanation:** The website shall clearly explain how `ofstartup` leverages AI agentic workflows, coding agents, and other advanced AI technologies to deliver services.
6.  **FR6: Benefits Highlighting:** The website shall highlight the benefits of `ofstartup`'s AI-driven approach, including faster delivery, reduced development costs, quick deployment, and superior quality.
7.  **FR7: Team Member Showcase:** The About page shall display information about key team members (Marshal Tudu, Zunaid Ahaal) with bios and professional visuals.
8.  **FR8: Social Proof Display:** The website shall integrate testimonials and partner/client logos to build trust and credibility.
9.  **FR9: OfImpact Service Page:** The website shall include a dedicated page for "OfImpact," detailing its role as an AI-driven service for integrating data science, automation, and analytics to reduce costs and development time.
10. **FR10: OfLogic Service Page:** The website shall include a dedicated page for "OfLogic," showcasing it as a neuromarketing service focusing on AI-driven, science-backed brand building and customer engagement.
11. **FR11: Portfolio Page:** The website shall include a dedicated "Portfolio" page to showcase projects, each with a title, link, and image. If the link is a valid website, a page view (e.g., a thumbnail or embed) of that website is loaded within the card. If the link fails or is invalid, it shall fallback to displaying the project image. If the image is also not available, it shall fallback to a generic icon.
12. **FR12: Robots.txt:** The website shall include a `robots.txt` file to guide search engine crawlers.
13. **FR13: Sitemap.xml:** The website shall include a `sitemap.xml` file to help search engines discover and index all relevant pages.
14. **FR14: Data Visualization Display:** The website shall display charts and data visualizations on relevant pages (e.g., Services, OfImpact, OfLogic) to illustrate benefits like cost savings, efficiency gains, and performance metrics.
15. **FR15: Founder Deck Page:** The website shall include a dedicated "Founder Deck" page showcasing the founders, Marshal Tudu (CTO) and Zunaid Ahaal (CEO), with their roles and LinkedIn profiles.

### Non Functional
1.  **NFR1: Design Aesthetic:** The website shall feature a modern, SaaS-inspired design with a purple-and-white color scheme, visually stunning elements, and a sophisticated aesthetic.
2.  **NFR2: Responsive Design:** The website shall be fully responsive and mobile-first, ensuring seamless display across various devices.
3.  **NFR3: Performance:** The website shall have fast load times and smooth performance.
4.  **NFR4: Accessibility:** The website shall adhere to accessibility standards (e.g., WCAG compliance).
5.  **NFR5: Cross-Browser Compatibility:** The website shall function correctly across major web browsers.
6.  **NFR6: Visual Elements:** The website shall incorporate high-quality, AI-generated images (neural networks, data flows, interconnected nodes) and icons.
7.  **NFR7: Interactive Elements:** The website shall feature advanced animations and interactive elements, including GSAP-driven hero section animations, scroll-triggered animations, and micro-interactions.
8.  **NFR8: Typography:** The website shall use modern, clean typography (e.g., Inter or Plus Jakarta Sans) with clear hierarchy.
9.  **NFR9: Background Grid Pattern:** A subtle, barely visible grid pattern shall be present in the background across all sections.
10. **NFR10: Theme Support:** The website shall support both light and dark themes, with a consistent visual identity across both.
11. **NFR11: Content Scannability:** Long-form content shall be structured for easy scannability using clear sectioning, short paragraphs, bullet points, and bolded key phrases.
12. **NFR12: SEO Optimization:** Content shall be optimized with SEO-friendly keywords (e.g., AI solutions, neuromarketing, data analytics, AI agentic workflows).
13. **NFR13: Server-Side/Client-Side Separation:** `page.tsx` files shall primarily contain server-side code and metadata, with all animations and interactive content implemented client-side.

## User Interface Design Goals

*   **Overall UX Vision:** To create a visually stunning, intuitive, and highly engaging user experience that reflects `ofstartup`'s cutting-edge AI capabilities and positions it as a leader in AI-driven service delivery. The design will be modern, sophisticated, and inspired by top-tier SaaS platforms like Scale AI, with a strong emphasis on dynamic visuals and seamless interactions.
*   **Key Interaction Paradigms:**
    *   **Immersive Scrolling:** Utilize scroll-triggered animations (GSAP) to reveal content dynamically and create a sense of depth and progression.
    *   **Subtle Micro-interactions:** Implement small, delightful animations on hover/click for buttons, links, and icons to enhance user feedback and engagement.
    *   **Clear Navigation:** Maintain a simple, sticky navigation for easy access to all key service areas.
    *   **Theme Switching:** Provide a seamless experience for users to switch between light and dark themes.
*   **Core Screens and Views:**
    *   Home Page (Landing Page)
    *   About Page
    *   Services Page (Overview of all services)
    *   OfImpact Page (Dedicated service detail page)
    *   OfLogic Page (Dedicated service detail page)
    *   Contact Page (displaying static contact info)
    *   Portfolio Page
    *   Founder Deck Page
*   **Accessibility:** WCAG AA
*   **Branding:**
    *   **Color Scheme:** Primary purple (`#8A2BE2` or `#6A0DAD`) and white (`#FFFFFF`) with a deep charcoal grey (`#1A1A1A`) for dark theme backgrounds and light grey (`#E0E0E0`) for text. Light theme will use white/off-white backgrounds with dark text and purple accents.
    *   **Typography:** Inter or Plus Jakarta Sans for all text, with clear hierarchy for headings and body.
    *   **Visual Elements:** High-quality, AI-generated imagery focusing on neural networks, data flows, interconnected nodes, and abstract representations of AI processes.
    *   **Background Grid:** A subtle, barely visible grid pattern will be present in the background across all sections.
*   **Target Device and Platforms:** Web Responsive

## Technical Assumptions

*   **Repository Structure:** (To be determined, assuming Monorepo for now unless specified otherwise)
*   **Service Architecture:** **Purely Frontend Application (No Backend)**
*   **Testing Requirements:** (To be determined, assuming Unit + Integration for now unless specified otherwise)
*   **Additional Technical Assumptions and Requests:**
    *   **Frontend Framework:** Next.js
    *   **Styling Framework:** Tailwind CSS
    *   **UI Component Library:** Shadcn UI, **Shadcn Charts**
    *   **Animation Libraries:** Framer Motion and GSAP (including ScrollTrigger)
    *   **Icons:** Lucide React
    *   **Chart Dependency:** Recharts (as required by Shadcn Charts)
    *   **Centralized Configuration File:** For storing static contact information (WhatsApp, call number, email, address, company name).
    *   **Content Management System (CMS):** (To be determined, e.g., Sanity or Contentful, for easy content updates)
    *   **Analytics Tracking:** (To be determined, e.g., Google Analytics - will be client-side implementation)

## Epic List

*   **Epic 1: Foundational Setup & Core Structure**
    *   **Goal:** Establish the Next.js project, configure essential development tools (Tailwind CSS, Shadcn UI, Framer Motion, GSAP, Lucide React), implement the basic responsive layout with light/dark theme support, and deploy a minimal functional website.
*   **Epic 2: Core Content Pages & Basic Navigation**
    *   **Goal:** Implement the Home, About, and main Services overview pages with their long-form content, basic styling, and the sticky navigation system, ensuring content scannability and initial SEO optimization.
*   **Epic 3: Specialized Service Pages & Enhanced Visuals**
    *   **Goal:** Develop the dedicated OfImpact and OfLogic pages, incorporating their unique visual requirements (e.g., brain graphic, dashboard mockups) and integrating initial GSAP-driven hero section animations.
*   **Epic 4: Advanced Interactivity & Dynamic Elements**
    *   **Goal:** Implement comprehensive GSAP-driven scroll-triggered animations, micro-interactions, and refine visual elements like the background grid pattern across all pages to create a highly engaging user experience.
*   **Epic 5: Functional Enhancements & Credibility**
    *   **Goal:** Implement static contact information display, social proof elements (testimonials, partner logos), and ensure full accessibility and cross-browser compatibility.
*   **Epic 6: Portfolio Showcase**
    *   **Goal:** Implement a dedicated Portfolio page to showcase `ofstartup`'s past projects, including dynamic loading of project details, intelligent handling of external links by attempting to embed page views, and robust fallback mechanisms for images and invalid links.
*   **Epic 7: Founder Deck Showcase**
    *   **Goal:** Implement a dedicated "Founder Deck" page to introduce the key individuals behind `ofstartup`, clearly presenting their roles and providing links to their professional profiles.

## Epic 1 Foundational Setup & Core Structure

**Expanded Goal:** This epic aims to lay the essential technical groundwork for the `ofstartup` website. It will involve setting up the Next.js project, configuring the chosen styling and animation libraries, establishing the core responsive layout, and implementing the initial light and dark theme support. The successful completion of this epic will result in a deployable, minimal functional website that serves as the foundation for all subsequent development.

#### Story 1.1: Project Initialization & Core Dependencies

As a developer,
I want to initialize a new Next.js project and install core dependencies,
so that I have a clean, modern foundation for building the `ofstartup` website.

**Acceptance Criteria:**
1.  The project is initialized as a Next.js application.
2.  Tailwind CSS is successfully installed and configured for styling.
3.  Shadcn UI is installed and integrated, ready for component generation.
4.  Framer Motion and GSAP are installed and configured for animation capabilities.
5.  Lucide React is installed for icon usage.
6.  The project successfully compiles and runs locally without errors.
7.  The project structure is clean and adheres to Next.js best practices.

#### Story 1.2: Basic Responsive Layout & Global Styles

As a user,
I want the website to have a basic responsive layout and global styles,
so that it looks consistent and functions well across different devices.

**Acceptance Criteria:**
1.  A global CSS file (e.g., `globals.css`) is set up with basic resets and font imports (Inter or Plus Jakarta Sans).
2.  A root layout component (`layout.tsx`) is created to define the overall page structure.
3.  The layout is responsive, adapting gracefully to mobile, tablet, and desktop screen sizes.
4.  The chosen typography (Inter or Plus Jakarta Sans) is correctly applied globally.
5.  The primary purple color is defined and accessible via Tailwind CSS.
6.  The `app/page.tsx` file is an empty, functional component, ready for content.

#### Story 1.3: Light and Dark Theme Implementation

As a user,
I want to be able to switch between a light and dark theme,
so that I can view the website in my preferred visual mode.

**Acceptance Criteria:**
1.  A theme provider component is implemented (e.g., using Next.js's `next-themes` or a custom context).
2.  A mechanism (e.g., a toggle button in a placeholder header) is available to switch between light and dark themes.
3.  The primary background and text colors correctly switch based on the selected theme (charcoal/white for dark, white/dark grey for light).
4.  The accent purple color remains consistent and visible in both themes.
5.  The theme preference is persisted (e.g., using local storage).

#### Story 1.4: Subtle Background Grid Pattern

As a user,
I want to see a subtle grid pattern in the background,
so that the website has a consistent, tech-infused aesthetic.

**Acceptance Criteria:**
1.  A barely visible grid pattern is implemented as a background element.
2.  The grid pattern is present across all sections of the website.
3.  The grid lines are extremely thin and have very low opacity (e.g., 0.02-0.05).
4.  The grid line color adapts appropriately for both light and dark themes (e.g., very dark grey/desaturated purple on dark, very light grey/desaturated purple on light).
5.  The grid pattern does not distract from the main content.

## Epic 2 Core Content Pages & Basic Navigation

**Expanded Goal:** This epic focuses on building out the foundational content pages of the `ofstartup` website. It includes implementing the Home, About, and main Services overview pages with their long-form content, applying basic styling, and establishing the persistent sticky navigation system. The aim is to ensure the core information is present, easily scannable, and ready for future enhancements, while also laying the groundwork for initial SEO.

#### Story 2.1: Home Page Content & Structure

As a user,
I want to see the main content and structure of the Home page,
so that I can immediately understand `ofstartup`'s core value proposition.

**Acceptance Criteria:**
1.  The Home page (`app/page.tsx`) is populated with a hero section containing placeholder headline, subheadline, and a primary CTA button.
2.  The page includes a basic structure for long-form content sections below the hero, ready for detailed text.
3.  The content uses the established typography and color scheme.
4.  The page is responsive and displays correctly across different screen sizes.

#### Story 2.2: About Page Content & Structure

As a user,
I want to learn about `ofstartup`'s story and team,
so that I can understand the company's background and expertise.

**Acceptance Criteria:**
1.  A dedicated About page (`app/about/page.tsx`) is created.
2.  The page includes sections for "Our Story" and "Our Team" with placeholder content.
3.  Placeholder elements for team member bios and visuals are present.
4.  The content uses the established typography and color scheme.
5.  The page is responsive and displays correctly across different screen sizes.

#### Story 2.3: Services Overview Page Content & Structure

As a user,
I want to see an overview of all services offered by `ofstartup`,
so that I can understand the breadth of their expertise.

**Acceptance Criteria:**
1.  A dedicated Services overview page (`app/services/page.tsx`) is created.
2.  The page includes distinct sections or card-based layouts for each service offering (e.g., Business Strategy, AI & ML Solutions, Product Development, Software Development, Data Analytics, Digital Transformation, AI Suite - Sales & Customer Support, AI Agent Development, AI Consultancy, NeuroAI Marketing - OfLogic).
3.  Placeholder content for each service description is present.
4.  The content uses the established typography and color scheme.
5.  The page is responsive and displays correctly across different screen sizes.

#### Story 2.4: Basic Sticky Navigation Bar

As a user,
I want to easily navigate between different sections of the website,
so that I can find the information I need quickly.

**Acceptance Criteria:**
1.  A sticky navigation bar component (`components/navbar.tsx`) is created and integrated into the main layout.
2.  The navigation bar remains visible at the top of the viewport as the user scrolls.
3.  It contains functional links to Home, About, Services, OfImpact, OfLogic, Contact, Portfolio, and Founder Deck pages.
4.  The navigation bar adheres to the established color scheme and typography, and supports both light and dark themes.
5.  The navigation is responsive and functions correctly on mobile devices (e.g., with a hamburger menu for smaller screens).

#### Story 2.5: Basic Footer Implementation

As a user,
I want to see essential company information and links at the bottom of every page,
so that I can access important resources.

**Acceptance Criteria:**
1.  A footer component (`components/footer.tsx`) is created and integrated into the main layout.
2.  The footer is present at the bottom of all pages.
3.  It includes copyright information for `ofstartup`.
4.  Placeholder sections for additional links (e.g., Privacy Policy, Terms of Service) are present.
5.  The footer adheres to the established color scheme and typography, and supports both light and dark themes.

## Epic 3 Specialized Service Pages & Enhanced Visuals

**Expanded Goal:** This epic will bring to life the specialized service pages for OfImpact and OfLogic, which are crucial for showcasing `ofstartup`'s unique AI-driven methodologies. It will involve populating these pages with their specific content and integrating the initial, impactful GSAP-driven hero section animations and unique visual elements (like the brain graphic for OfLogic and dashboard mockups for OfImpact). This epic will significantly enhance the visual appeal and communication of `ofstartup`'s core offerings.

#### Story 3.1: OfImpact Page Content & Structure

As a user,
I want to understand the details of the OfImpact service,
so that I can see how it integrates data science, automation, and analytics to reduce costs.

**Acceptance Criteria:**
1.  A dedicated OfImpact page (`app/ofimpact/page.tsx`) is created.
2.  The page includes sections for "What is OfImpact?", "Key Benefits," and "Use Cases" with placeholder content.
3.  A placeholder for a sleek admin dashboard visual is present.
4.  The content uses the established typography and color scheme.
5.  The page is responsive and displays correctly across different screen sizes.

#### Story 3.2: OfLogic Page Content & Structure

As a user,
I want to understand the details of the OfLogic service,
so that I can see how it uses neuromarketing for brand building and customer engagement.

**Acceptance Criteria:**
1.  A dedicated OfLogic page (`app/oflogic/page.tsx`) is created.
2.  The page includes sections for "What is OfLogic?", "Key Features," "Why OfLogic?", and "Process" with placeholder content.
3.  A placeholder for a vibrant brain graphic with neural networks is present.
4.  The content uses the established typography and color scheme.
5.  The page is responsive and displays correctly across different screen sizes.

#### Story 3.3: Hero Section Animation for Home Page (GSAP)

As a user,
I want the Home page hero section to be visually engaging,
so that I am immediately captivated by the website's dynamic nature.

**Acceptance Criteria:**
1.  The main headline and subheadline in the Home page hero section animate in sequentially using GSAP (e.g., character/word reveal, fade-up).
2.  The primary CTA button animates in after the text (e.g., slight scale-up and fade-in).
3.  The animation is smooth and performs well across devices.
4.  The animation is integrated with the light and dark theme.

#### Story 3.4: Hero Section Animation for OfImpact Page (GSAP)

As a user,
I want the OfImpact page hero section to be visually engaging,
so that it immediately conveys the advanced nature of the service.

**Acceptance Criteria:**
1.  The main headline and subheadline in the OfImpact page hero section animate in using GSAP.
2.  The "Discover OfImpact" CTA button animates in after the text.
3.  The animation is smooth and performs well across devices.
4.  The animation is integrated with the light and dark theme.

#### Story 3.5: Hero Section Animation for OfLogic Page (GSAP)

As a user,
I want the OfLogic page hero section to be visually engaging,
so that it immediately highlights the innovative approach to neuromarketing.

**Acceptance Criteria:**
1.  The main headline and subheadline in the OfLogic page hero section animate in using GSAP.
2.  The "Launch Your Campaign" CTA button animates in after the text.
3.  The animation is smooth and performs well across devices.
4.  The animation is integrated with the light and dark theme.

#### Story 3.6: Integration of Unique Visuals for OfImpact & OfLogic

As a user,
I want to see the specific visual elements that represent OfImpact and OfLogic,
so that I can better understand their unique offerings.

**Acceptance Criteria:**
1.  A placeholder for a sleek admin dashboard visual is integrated into the OfImpact page, adhering to the design aesthetic.
2.  A placeholder for a vibrant brain graphic with purple and white neural networks is integrated into the OfLogic page, prominently placed and adhering to the design aesthetic.

## Epic 4 Advanced Interactivity & Dynamic Elements

**Expanded Goal:** This epic is dedicated to elevating the user experience through sophisticated animations and interactive elements across the entire website. It will involve implementing comprehensive GSAP-driven scroll-triggered animations for content reveals, adding delightful micro-interactions to various UI elements, and refining the subtle background grid pattern to ensure it dynamically enhances the visual appeal without distracting from content. The successful completion of this epic will result in a highly engaging and premium feel for the `ofstartup` website.

#### Story 4.1: Global Scroll-Triggered Content Reveals (GSAP)

As a user,
I want content sections to animate into view as I scroll,
so that the website feels dynamic and engaging.

**Acceptance Criteria:**
1.  All major content sections (e.g., "Our Story," "Service Offerings," "Key Benefits") animate into view as they enter the viewport.
2.  Animations are smooth and varied (e.g., slide-in from bottom/side, fade-up, subtle scale-up).
3.  Animations are implemented using GSAP's ScrollTrigger.
4.  Animations perform well across devices and do not cause jank.

#### Story 4.2: Micro-interactions for Buttons & Links

As a user,
I want interactive elements to provide visual feedback,
so that I feel engaged and understand my actions are registered.

**Acceptance Criteria:**
1.  All primary and secondary CTA buttons have a subtle, smooth hover effect (e.g., background fill, slight scale, text shift).
2.  All text links have a subtle hover effect (e.g., animated underline, background highlight).
3.  Micro-interactions are implemented using GSAP or Framer Motion.
4.  Micro-interactions are consistent across the website and work correctly in both light and dark themes.

#### Story 4.3: Micro-interactions for Cards & Icons

As a user,
I want visual elements like cards and icons to be interactive,
so that the website feels polished and responsive.

**Acceptance Criteria:**
1.  Service cards, team member cards, and any other card-based layouts have a subtle hover effect (e.g., slight lift, subtle shadow, border glow).
2.  Icons (e.g., service icons, social media icons) have a subtle hover effect (e.g., slight scale, color change, rotation).
3.  Micro-interactions are implemented using GSAP or Framer Motion.
4.  Micro-interactions are consistent across the website and work correctly in both light and dark themes.

#### Story 4.4: Dynamic Background Grid Pattern

As a user,
I want the background grid pattern to subtly respond to my interaction,
so that it enhances the dynamic feel of the website.

**Acceptance Criteria:**
1.  The background grid pattern subtly shifts its opacity or color intensity as different content sections are scrolled into view.
2.  The animation is smooth and non-distracting, implemented using GSAP.
3.  The dynamic behavior is consistent across all pages and themes.

#### Story 4.5: Navigation Bar Interactions

As a user,
I want the navigation bar to be visually responsive,
so that it feels integrated with the scrolling experience.

**Acceptance Criteria:**
1.  The sticky navigation bar subtly changes its appearance (e.g., shrinks in height, changes background opacity/color) as the user scrolls down the page.
2.  The active page link in the navigation has a distinct, animated indicator (e.g., underline, background highlight) that smoothly transitions.
3.  Any dropdown menus in the navigation have smooth slide-down or fade-in animations.

## Epic 5 Functional Enhancements & Credibility

**Expanded Goal:** This final epic focuses on implementing crucial functional elements and building trust and authority for the `ofstartup` website. It includes displaying static contact information, social proof elements (testimonials, partner logos), and ensuring the website meets high standards of accessibility and cross-browser compatibility. The successful completion of this epic will result in a fully functional, credible, and user-friendly website ready for deployment.

#### Story 5.1: Contact Page & Static Contact Information Display

As a user,
I want to easily find `ofstartup`'s contact information on a dedicated page,
so that I can reach out directly.

**Acceptance Criteria:**
1.  A dedicated Contact page (`app/contact/page.tsx`) is created.
2.  The page displays static contact information including WhatsApp number, call number, email address, company name, and physical address.
3.  This contact information is sourced from a centralized configuration file.
4.  The Contact page adheres to the established design aesthetic and responsiveness.

#### Story 5.2: Testimonial Integration

As a user,
I want to see what other clients say about `ofstartup`'s services,
so that I can build trust and confidence in their expertise.

**Acceptance Criteria:**
1.  A testimonial section is implemented on relevant pages (e.g., Home, Services, OfImpact, OfLogic).
2.  Testimonials are displayed in an engaging format (e.g., a slider or carousel).
3.  Each testimonial includes the client's quote and attribution (e.g., name, company).
4.  The testimonial display is responsive and visually appealing, adhering to the design aesthetic.
5.  If a slider/carousel is used, it has smooth transitions (can leverage GSAP/Framer Motion).

#### Story 5.3: Partner/Client Logo Display

As a user,
I want to see `ofstartup`'s partners or clients,
so that I can understand their industry connections and credibility.

**Acceptance Criteria:**
1.  A section for displaying partner or client logos is implemented on relevant pages (e.g., Home, About).
2.  Logos are displayed in a clean, organized grid or carousel format.
3.  The logo display is responsive and visually appealing.
4.  Logos subtly animate on hover or load with a fade-in effect.

#### Story 5.4: Website Accessibility (WCAG AA)

As a user,
I want the website to be accessible to everyone,
so that I can easily navigate and consume content regardless of my abilities.

**Acceptance Criteria:**
1.  The website adheres to WCAG 2.1 AA guidelines.
2.  All interactive elements are keyboard navigable.
3.  Appropriate ARIA attributes are used for semantic meaning.
4.  Color contrast ratios meet WCAG AA standards for both light and dark themes.
5.  Images have descriptive alt text.
6.  Form fields have proper labels and error messages are clear.

#### Story 5.5: Cross-Browser Compatibility

As a user,
I want the website to function correctly across different web browsers,
so that I can access it regardless of my browser choice.

**Acceptance Criteria:**
1.  The website displays and functions correctly on the latest stable versions of major browsers (e.g., Chrome, Firefox, Edge, Safari).
2.  All visual elements, animations, and interactive features render consistently across supported browsers.

## Epic 6 Portfolio Showcase

**Expanded Goal:** This epic will implement a dedicated Portfolio page to showcase `ofstartup`'s past projects. It will include dynamic loading of project details, intelligent handling of external links by attempting to embed page views, and robust fallback mechanisms for images and invalid links, ensuring a professional and informative presentation of `ofstartup`'s work.

#### Story 6.1: Portfolio Page Content & Structure

As a user,
I want to view a list of `ofstartup`'s projects on a dedicated Portfolio page,
so that I can see examples of their work.

**Acceptance Criteria:**
1.  A dedicated Portfolio page (`app/portfolio/page.tsx`) is created.
2.  The page displays a grid or list of project cards.
3.  Each project card includes a title, a link, and a placeholder for an image.
4.  The content uses the established typography and color scheme.
5.  The page is responsive and displays correctly across different screen sizes.

#### Story 6.2: Dynamic Project Card Content & Link Handling

As a user,
I want to see a preview of linked websites directly within the portfolio cards,
so that I can quickly understand the nature of the project without leaving the page.

**Acceptance Criteria:**
1.  For each project card, if the associated link is a valid external website, a page view (e.g., a thumbnail or embed) of that website is loaded and displayed within the card.
2.  If the link is invalid (e.g., `#`, broken URL) or fails to load, the card falls back to displaying the project image.
3.  If the project image is also not available, the card falls back to displaying a generic placeholder icon.
4.  The dynamic loading and fallback logic is robust and handles various link and image states gracefully.
5.  The page view/image/icon is displayed within the card's design, maintaining visual consistency.

## Epic 7 Founder Deck Showcase

**Expanded Goal:** This epic will implement a dedicated "Founder Deck" page to introduce the key individuals behind `ofstartup`. It will clearly present their roles and provide links to their professional profiles.

#### Story 7.1: Founder Deck Page Content & Structure

As a user,
I want to learn about the founders of `ofstartup`,
so that I can understand their background and leadership.

**Acceptance Criteria:**
1.  A dedicated Founder Deck page (`app/founder-deck/page.tsx`) is created.
2.  The page displays information for Marshal Tudu (CTO) and Zunaid Ahaal (CEO).
3.  Each founder's entry includes their name, role, and a link to their LinkedIn profile (if available).
4.  The content uses the established typography and color scheme.
5.  The page is responsive and displays correctly across different screen sizes.