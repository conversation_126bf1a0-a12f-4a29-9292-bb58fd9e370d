'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { PortfolioProject, PortfolioParser, PaginatedPortfolioData, PortfolioFilters } from '@/lib/portfolio-parser';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from './pagination';
import { Button } from './button';
import {
  Calendar,
  Building2,
  Clock,
  TrendingUp,
  Code,
  Search,
  Loader2,
  Eye,
  Sparkles
} from 'lucide-react';

interface PortfolioCardProps {
  project: PortfolioProject;
  isExpanded: boolean;
  onToggleExpand: (projectId: string) => void;
}

import { VisualEffects } from './visual-effects';

const PortfolioCard: React.FC<PortfolioCardProps> = ({ project, isExpanded, onToggleExpand }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short'
    });
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'High':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getTechnologies = () => {
    return project.technologies.split(',').map(tech => tech.trim()).filter(tech => tech).slice(0, 3);
  };


  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className={`relative overflow-hidden group transition-all duration-500 border rounded-2xl h-full ${
        isExpanded
          ? 'col-span-1 md:col-span-2 lg:col-span-3 shadow-2xl shadow-purple-500/30 border-purple-500/50 dark:border-purple-400/50 bg-white/95 dark:bg-black/95 backdrop-blur-xl'
          : 'hover:shadow-xl hover:-translate-y-2 border-purple-500/20 dark:border-purple-400/20 hover:border-purple-500/40 dark:hover:border-purple-400/40 bg-white/90 dark:bg-black/90 backdrop-blur-lg'
      }`}
      whileHover={!isExpanded ? { scale: 1.02 } : {}}
    >
      {/* Enhanced glow effect */}
      <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/20 via-blue-600/20 to-purple-600/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
      <VisualEffects effects={['glow']} intensity={'medium'} className="absolute inset-0 z-0" />
      <div className={`relative z-10 grid transition-all duration-300 ${
        isExpanded
          ? 'lg:grid-cols-2 gap-0'
          : 'grid-cols-1'
      }`}>
        
        {/* Left side - Always visible content */}
        <div className="flex flex-col h-full">
          {/* Enhanced Header with animated gradient */}
          <motion.div
            className="relative bg-gradient-to-br from-purple-600 via-purple-700 to-blue-700 p-6 text-white overflow-hidden"
            whileHover={{ scale: 1.01 }}
            transition={{ duration: 0.2 }}
          >
            {/* Animated background pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 animate-pulse"></div>
            </div>

            <div className="relative z-10">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <motion.h3
                    className="text-xl font-bold mb-2 flex items-center gap-2"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <Sparkles className="w-5 h-5 text-yellow-300" />
                    {project.title}
                  </motion.h3>
                  <motion.div
                    className="flex items-center gap-2 text-purple-100"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <Building2 className="w-4 h-4" />
                    <span className="text-sm font-medium">{project.domain}</span>
                  </motion.div>
                </div>
                <motion.div
                  className={`px-3 py-1 rounded-full text-xs font-bold ${getComplexityColor(project.complexity_level)} bg-opacity-90 shadow-lg`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  {project.complexity_level}
                </motion.div>
              </div>

              <motion.div
                className="flex items-center justify-end text-sm text-purple-100"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <span className="font-bold bg-white/20 px-2 py-1 rounded-md">{project.year}</span>
              </motion.div>
            </div>
          </motion.div>

          {/* Enhanced Content */}
          <div className="p-6 bg-white/50 dark:bg-black/50 backdrop-blur-xl border-t border-purple-500/10 flex-1 flex flex-col">
            <motion.p
              className={`text-secondary mb-6 text-sm leading-relaxed ${
                isExpanded ? '' : 'line-clamp-3'
              }`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              {project.description}
            </motion.p>

            {/* Enhanced Quick Metrics */}
            <motion.div
              className="grid grid-cols-2 gap-4 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <div className="flex items-center gap-2 text-sm text-tertiary bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-500/20">
                <Clock className="w-4 h-4 text-purple-600" />
                <span className="font-medium">{project.delivery_time_days} days</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-tertiary bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-500/20">
                <Calendar className="w-4 h-4 text-purple-600" />
                <span className="font-medium">{formatDate(project.end_date)}</span>
              </div>
            </motion.div>

            {/* Enhanced Technologies Preview */}
            <motion.div
              className="mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <div className="flex items-center gap-2 mb-3">
                <Code className="w-4 h-4 text-purple-600" />
                <span className="text-sm font-bold text-tertiary">Tech Stack:</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {getTechnologies().slice(0, isExpanded ? getTechnologies().length : 3).map((tech, index) => (
                  <motion.span
                    key={index}
                    className="px-3 py-1 bg-gradient-to-r from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30 text-purple-700 dark:text-purple-300 rounded-full text-xs font-bold border border-purple-500/20 hover:border-purple-500/40 transition-all duration-200 hover:scale-105"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    whileHover={{ scale: 1.1 }}
                  >
                    {tech}
                  </motion.span>
                ))}
                {!isExpanded && project.technologies.split(',').length > 3 && (
                  <motion.span
                    className="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full text-xs font-medium border border-gray-300 dark:border-gray-600"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 1.1 }}
                  >
                    +{project.technologies.split(',').length - 3} more
                  </motion.span>
                )}
              </div>
            </motion.div>

            {/* Spacer to push button to bottom */}
            <div className="flex-1"></div>

            {/* Enhanced Action Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 }}
            >
              <Button
                onClick={() => onToggleExpand(project.project_id)}
                className="w-full transition-all duration-300 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-3 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105"
                variant={isExpanded ? "secondary" : "default"}
              >
                {isExpanded ? (
                  <>
                    <TrendingUp className="w-4 h-4 mr-2 rotate-180" />
                    <span>Collapse Details</span>
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4 mr-2" />
                    View Full Details
                  </>
                )}
              </Button>
            </motion.div>
          </div>
        </div>

        {/* Right side - Enhanced Expanded details */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 50 }}
              transition={{ duration: 0.4 }}
              className="border-l border-purple-500/20 bg-gradient-to-br from-white/80 to-purple-50/50 dark:from-black/80 dark:to-purple-900/20 backdrop-blur-xl"
            >
              <div className="p-6">
                <motion.h4
                  className="text-lg font-bold text-primary mb-6 flex items-center gap-2"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Sparkles className="w-5 h-5 text-purple-600" />
                  Project Deep Dive
                </motion.h4>
              
              {/* Enhanced Detailed Timeline */}
              <motion.div
                className="mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <h5 className="flex items-center gap-2 font-bold text-secondary mb-4">
                  <Calendar className="w-5 h-5 text-purple-600" />
                  Project Timeline
                </h5>
                <div className="space-y-3 text-sm bg-white/50 dark:bg-black/30 p-4 rounded-xl border border-purple-500/20">
                  <div className="flex justify-between items-center p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <span className="text-tertiary font-medium">Start Date:</span>
                    <span className="font-bold text-purple-600">{formatDate(project.start_date)}</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <span className="text-tertiary font-medium">End Date:</span>
                    <span className="font-bold text-purple-600">{formatDate(project.end_date)}</span>
                  </div>
                  <div className="flex justify-between items-center p-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <span className="text-tertiary font-medium">Duration:</span>
                    <span className="font-bold text-purple-600">{project.delivery_time_days} days</span>
                  </div>
                </div>
              </motion.div>

              {/* All Technologies */}
              <div className="mb-6">
                <h5 className="flex items-center gap-2 font-medium text-secondary mb-3">
                  <Code className="w-4 h-4 text-purple-600" />
                  Full Technology Stack
                </h5>
                <div className="flex flex-wrap gap-2">
                  {getTechnologies().map((tech, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md text-sm font-medium shadow-sm"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Project Stats */}
              <div className="grid grid-cols-1 gap-4 mb-6">
                <div className="p-3 bg-white dark:bg-gray-700 rounded-lg shadow-sm">
                  <div className="flex items-center gap-2 mb-1">
                    <TrendingUp className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium text-secondary">Complexity</span>
                  </div>
                  <div className={`inline-block px-2 py-1 rounded text-sm font-medium ${getComplexityColor(project.complexity_level)}`}>
                    {project.complexity_level}
                  </div>
                </div>
              </div>

              {/* Project Actions */}
              <div className="space-y-3">
                {project.project_url !== '#' && (
                  <Button asChild className="w-full">
                    <a
                      href={project.project_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center gap-2"
                    >
                      Visit Project
                      <TrendingUp className="w-4 h-4" />
                    </a>
                  </Button>
                )}
                <Button variant="outline" className="w-full" asChild>
                  <a href="/contact" className="flex items-center justify-center gap-2">
                    Start Similar Project
                    <Building2 className="w-4 h-4" />
                  </a>
                </Button>
              </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

interface SearchSectionProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  isLoading: boolean;
}

const SearchSection: React.FC<SearchSectionProps> = ({
  searchQuery,
  onSearchChange,
  isLoading
}) => {
  const [localQuery, setLocalQuery] = useState(searchQuery);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange(localQuery);
  };

  return (
    <div className="mb-8">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search projects by title, description, domain, or technology..."
            value={localQuery}
            onChange={(e) => setLocalQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-800 text-primary"
          />
        </div>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : <Search className="w-4 h-4" />}
        </Button>
      </form>
    </div>
  );
};

interface PortfolioGridPaginatedProps {
  initialProjectsPerPage?: number;
}

export const PortfolioGridPaginated: React.FC<PortfolioGridPaginatedProps> = ({
  initialProjectsPerPage = 12
}) => {
  const [portfolioData, setPortfolioData] = useState<PaginatedPortfolioData | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [projectsPerPage, setProjectsPerPage] = useState(initialProjectsPerPage);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCardId, setExpandedCardId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);


  // Load portfolio data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        const filters: PortfolioFilters = searchQuery ? { searchQuery } : {};
        const data = await PortfolioParser.getPaginatedData(currentPage, projectsPerPage, filters);
        setPortfolioData(data);
      } catch (error) {
        console.error('Error loading portfolio data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [currentPage, projectsPerPage, searchQuery]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when search changes
  };

  const clearSearch = () => {
    setSearchQuery('');
    setCurrentPage(1);
  };

  const handleToggleExpand = (projectId: string) => {
    setExpandedCardId(expandedCardId === projectId ? null : projectId);
  };

  if (isLoading && !portfolioData) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="w-8 h-8 animate-spin text-purple-600" />
          <p className="text-secondary">Loading portfolio projects...</p>
        </div>
      </div>
    );
  }

  if (!portfolioData || portfolioData.projects.length === 0) {
    return (
      <div className="text-center py-20">
        <div className="mb-6">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-primary mb-2">No Projects Found</h3>
          <p className="text-secondary">
            {searchQuery
              ? "No projects match your search query. Try different keywords."
              : "No portfolio projects available at the moment."}
          </p>
        </div>
        {searchQuery && (
          <Button onClick={clearSearch}>Clear Search</Button>
        )}
      </div>
    );
  }

  return (
    <div>
      {/* Search Section */}
      <SearchSection
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        isLoading={isLoading}
      />

      {/* Results Summary */}
      <div className="flex justify-between items-center mb-6">
        <div className="text-secondary">
          Showing {((currentPage - 1) * projectsPerPage) + 1} to{' '}
          {Math.min(currentPage * projectsPerPage, portfolioData.totalProjects)} of{' '}
          {portfolioData.totalProjects} projects
        </div>
        <div className="flex items-center gap-4">
          <select
            value={projectsPerPage}
            onChange={(e) => {
              setProjectsPerPage(parseInt(e.target.value));
              setCurrentPage(1);
            }}
            className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-800 text-primary focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          >
            <option value={6}>6 per page</option>
            <option value={12}>12 per page</option>
            <option value={24}>24 per page</option>
            <option value={48}>48 per page</option>
          </select>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-purple-600" />
        </div>
      )}

      {/* Portfolio Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12 items-stretch">
        {portfolioData.projects.map((project) => (
          <PortfolioCard
            key={project.project_id}
            project={project}
            isExpanded={expandedCardId === project.project_id}
            onToggleExpand={handleToggleExpand}
          />
        ))}
      </div>

      {/* Pagination */}
      {portfolioData.totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              {currentPage > 1 && (
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => handlePageChange(currentPage - 1)}
                    className="cursor-pointer"
                  />
                </PaginationItem>
              )}
              
              {/* First page */}
              {currentPage > 3 && (
                <>
                  <PaginationItem>
                    <PaginationLink
                      onClick={() => handlePageChange(1)}
                      className="cursor-pointer"
                    >
                      1
                    </PaginationLink>
                  </PaginationItem>
                  {currentPage > 4 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}
                </>
              )}

              {/* Current page range */}
              {Array.from(
                { length: Math.min(5, portfolioData.totalPages) },
                (_, i) => {
                  const pageNum = Math.max(1, currentPage - 2) + i;
                  if (pageNum > portfolioData.totalPages) return null;
                  return (
                    <PaginationItem key={pageNum}>
                      <PaginationLink
                        onClick={() => handlePageChange(pageNum)}
                        isActive={pageNum === currentPage}
                        className="cursor-pointer"
                      >
                        {pageNum}
                      </PaginationLink>
                    </PaginationItem>
                  );
                }
              ).filter(Boolean)}

              {/* Last page */}
              {currentPage < portfolioData.totalPages - 2 && (
                <>
                  {currentPage < portfolioData.totalPages - 3 && (
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )}
                  <PaginationItem>
                    <PaginationLink
                      onClick={() => handlePageChange(portfolioData.totalPages)}
                      className="cursor-pointer"
                    >
                      {portfolioData.totalPages}
                    </PaginationLink>
                  </PaginationItem>
                </>
              )}

              {currentPage < portfolioData.totalPages && (
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => handlePageChange(currentPage + 1)}
                    className="cursor-pointer"
                  />
                </PaginationItem>
              )}
            </PaginationContent>
          </Pagination>
        </div>
      )}

    </div>
  );
};

export default PortfolioGridPaginated;