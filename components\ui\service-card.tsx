'use client';

import React, { useRef, useEffect } from 'react';
import Link from 'next/link';

interface ServiceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  ctaText?: string;
  ctaHref?: string;
  className?: string;
  size?: 'default' | 'large';
  variant?: 'default' | 'featured';
  style?: React.CSSProperties;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  icon,
  title,
  description,
  ctaText = 'Learn More',
  ctaHref = '#',
  className = '',
  size = 'default',
  variant = 'default',
  style
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const card = cardRef.current;
    const iconElement = iconRef.current;
    
    if (!card || !iconElement) return;

    const handleMouseEnter = () => {
      card.style.transform = 'translateY(-8px)';
      card.style.boxShadow = '0 20px 60px rgba(138, 43, 226, 0.25), 0 0 0 1px rgba(138, 43, 226, 0.1)';
      card.style.borderColor = 'rgba(138, 43, 226, 0.4)';

      iconElement.style.transform = 'scale(1.1) rotate(5deg)';
    };

    const handleMouseLeave = () => {
      card.style.transform = 'translateY(0px)';
      card.style.boxShadow = 'var(--shadow-md)';
      card.style.borderColor = 'var(--border-color)';
      
      iconElement.style.transform = 'scale(1) rotate(0deg)';
    };

    card.addEventListener('mouseenter', handleMouseEnter);
    card.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      card.removeEventListener('mouseenter', handleMouseEnter);
      card.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  const sizeClasses = {
    default: 'w-full max-w-sm min-h-[400px]',
    large: 'w-full max-w-md min-h-[450px]'
  };

  const variantClasses = {
    default: '',
    featured: 'card-glow'
  };

  return (
    <div
      ref={cardRef}
      className={`
        card relative overflow-hidden
        ${sizeClasses[size]}
        ${variantClasses[variant]}
        ${className}
      `}
      style={style}
    >
      {/* Background Pattern for Featured Cards */}
      {variant === 'featured' && (
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600 to-purple-800" />
          <div 
            className="absolute inset-0"
            style={{
              backgroundImage: `radial-gradient(circle at 20% 50%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                               radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                               radial-gradient(circle at 40% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 50%)`
            }}
          />
        </div>
      )}

      {/* Card Content */}
      <div className="relative z-10 p-6 h-full flex flex-col">
        
        {/* Icon Area */}
        <div className="mb-6">
          <div
            ref={iconRef}
            className={`
              w-16 h-16 rounded-xl flex items-center justify-center
              transition-all duration-200 ease-out border
              ${variant === 'featured'
                ? 'bg-purple-600 text-white shadow-lg border-purple-500/50'
                : 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 border-purple-500/20'
              }
            `}
          >
            {icon}
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 flex flex-col">
          <h3 className="headline-large text-primary mb-3 font-semibold">
            {title}
          </h3>
          
          <p className="body-regular text-secondary mb-6 flex-1 leading-relaxed">
            {description}
          </p>

          {/* CTA Link */}
          <Link
            href={ctaHref}
            className={`
              inline-flex items-center body-small font-medium
              transition-all duration-200 ease-out
              hover:translate-x-1 group
              ${variant === 'featured'
                ? 'text-purple-600 dark:text-purple-400'
                : 'text-purple-600 dark:text-purple-400'
              }
            `}
          >
            <span className="mr-2">{ctaText}</span>
            <svg 
              className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M17 8l4 4m0 0l-4 4m4-4H3" 
              />
            </svg>
          </Link>
        </div>
      </div>

      {/* Hover Glow Effect */}
      <div className="absolute inset-0 opacity-0 transition-opacity duration-300 pointer-events-none hover:opacity-100">
        <div 
          className="absolute inset-0 rounded-xl"
          style={{
            background: 'radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%), rgba(138, 43, 226, 0.1) 0%, transparent 50%)'
          }}
        />
      </div>
    </div>
  );
};

interface ServiceCardGridProps {
  services: Array<{
    icon: React.ReactNode;
    title: string;
    description: string;
    ctaText?: string;
    ctaHref?: string;
    featured?: boolean;
  }>;
  className?: string;
}

export const ServiceCardGrid: React.FC<ServiceCardGridProps> = ({
  services,
  className = ''
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 ${className}`}>
      {services.map((service, index) => (
        <ServiceCard
          key={index}
          icon={service.icon}
          title={service.title}
          description={service.description}
          ctaText={service.ctaText}
          ctaHref={service.ctaHref}
          variant={service.featured ? 'featured' : 'default'}
          className="animate-fade-up"
          style={{ animationDelay: `${index * 150}ms` } as React.CSSProperties}
        />
      ))}
    </div>
  );
};

export default ServiceCard;
