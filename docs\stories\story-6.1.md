### Epic 6: Portfolio Showcase

*   **Story 6.1: Portfolio Page with Dynamic Project Cards**
    *   **Status:** Not Started
    *   **Story:** As a user, I want to view a dedicated Portfolio page showcasing `ofstartup`'s projects, with each project card dynamically handling external links and providing robust fallbacks, so that I can easily explore examples of their work.
    *   **Acceptance Criteria:**
        1.  A dedicated Portfolio page (`app/portfolio/page.tsx`) is created with a responsive grid or list layout for project cards.
        2.  Each project card includes a title, a link, and a placeholder for an image.
        3.  Logic is implemented within each project card to dynamically load a page view (thumbnail/embed) of an external website if the project link is valid.
        4.  Robust fallback logic is implemented: if the link is invalid or fails, the card displays the project image; if the image is also unavailable, it displays a generic placeholder icon.
        5.  The Portfolio page and its project cards adhere to the established design aesthetic and responsiveness.
    *   **Tasks:**
        *   [ ] Create `app/portfolio/page.tsx`.
        *   [ ] Define `PortfolioItem` interface and create a `portfolio.json` data file.
        *   [ ] Create a `PortfolioCard` component to display project details.
        *   [ ] Implement logic within `PortfolioCard` to attempt loading a page view/thumbnail for valid external links.
        *   [ ] Implement fallback logic for invalid links (display project image).
        *   [ ] Implement fallback logic for missing images (display generic icon).
        *   [ ] Ensure the Portfolio page displays a responsive grid/list of `PortfolioCard` components.
    *   **Dev Notes:**
    *   **Testing:**
    *   **QA Results:**