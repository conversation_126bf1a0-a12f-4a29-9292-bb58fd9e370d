### Epic 7: <PERSON> Deck Showcase

*   **Story 7.1: Founder <PERSON> Page Implementation**
    *   **Status:** Not Started
    *   **Story:** As a user, I want to learn about the founders of `ofstartup` on a dedicated page, so that I can understand their background and leadership.
    *   **Acceptance Criteria:**
        1.  A dedicated Founder Deck page (`app/founder-deck/page.tsx`) is created.
        2.  The page displays information for <PERSON> (CTO) and <PERSON><PERSON><PERSON> (CEO).
        3.  Each founder's entry includes their name, role, and a clickable link to their LinkedIn profile (if available).
        4.  The page applies the established typography and color scheme and is fully responsive across various screen sizes.
    *   **Tasks:**
        *   [ ] Create `app/founder-deck/page.tsx`.
        *   [ ] Add founder information (<PERSON>, <PERSON><PERSON><PERSON>) including name, role, and LinkedIn links.
        *   [ ] Ensure LinkedIn links are clickable.
        *   [ ] Add placeholders for founder visuals (headshots/avatars).
        *   [ ] Ensure the page uses global styling and is responsive.
    *   **Dev Notes:**
    *   **Testing:**
    *   **QA Results:**