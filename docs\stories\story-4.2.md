### Epic 4: Advanced Interactivity & Dynamic Elements

*   **Story 4.2: Comprehensive Micro-interactions**
    *   **Status:** Not Started
    *   **Story:** As a user, I want all interactive elements (buttons, links, cards, icons) to provide subtle, engaging visual feedback on hover, so that the website feels polished, responsive, and intuitive.
    *   **Acceptance Criteria:**
        1.  All primary and secondary CTA buttons have a smooth hover effect (e.g., background fill, slight scale).
        2.  All text links have a subtle hover effect (e.g., animated underline, background highlight).
        3.  Service cards, team member cards, portfolio cards, and icons have subtle hover effects (e.g., slight lift, shadow, scale, color change).
        4.  All micro-interactions are consistent across the website and function correctly in both light and dark themes.
    *   **Tasks:**
        *   [ ] Implement hover effects for buttons using CSS transitions or GSAP.
        *   [ ] Implement hover effects for text links (underline/highlight).
        *   [ ] Implement hover effects for cards (lift/shadow/glow).
        *   [ ] Implement hover effects for icons (scale/color change/rotation).
        *   [ ] Ensure all micro-interactions are theme-aware and consistent.
    *   **Dev Notes:**
    *   **Testing:**
    *   **QA Results:**