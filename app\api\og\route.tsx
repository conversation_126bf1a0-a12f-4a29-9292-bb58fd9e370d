import { ImageResponse } from 'next/og';
import { NextRequest } from 'next/server';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Get parameters from URL
    const title = searchParams.get('title') || 'ofstartup.ai - AI-Powered Innovation';
    const description = searchParams.get('description') || 'Transform your business with cutting-edge AI solutions';
    const page = searchParams.get('page') || 'home';

    // Load the logo
    const logoResponse = await fetch(new URL('/logo.webp', request.url));
    const logoArrayBuffer = await logoResponse.arrayBuffer();

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'white',
            backgroundImage: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
            position: 'relative',
          }}
        >
          {/* Background Pattern */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `
                linear-gradient(rgba(139, 92, 246, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(139, 92, 246, 0.03) 1px, transparent 1px)
              `,
              backgroundSize: '24px 24px',
            }}
          />
          
          {/* Content Container */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              textAlign: 'center',
              padding: '80px',
              maxWidth: '1000px',
              position: 'relative',
              zIndex: 1,
            }}
          >
            {/* Logo */}
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '40px',
              }}
            >
              <img
                src={`data:image/webp;base64,${Buffer.from(logoArrayBuffer).toString('base64')}`}
                alt="ofstartup.ai Logo"
                width="80"
                height="80"
                style={{
                  marginRight: '20px',
                }}
              />
              <div
                style={{
                  fontSize: '48px',
                  fontWeight: 'bold',
                  color: '#1f2937',
                  fontFamily: 'Inter, sans-serif',
                }}
              >
                ofstartup.ai
              </div>
            </div>

            {/* Title */}
            <div
              style={{
                fontSize: '56px',
                fontWeight: 'bold',
                color: '#1f2937',
                lineHeight: '1.1',
                marginBottom: '24px',
                fontFamily: 'Inter, sans-serif',
                textAlign: 'center',
              }}
            >
              {title}
            </div>

            {/* Description */}
            <div
              style={{
                fontSize: '24px',
                color: '#6b7280',
                lineHeight: '1.4',
                marginBottom: '40px',
                fontFamily: 'Inter, sans-serif',
                textAlign: 'center',
                maxWidth: '800px',
              }}
            >
              {description}
            </div>

            {/* Purple Accent */}
            <div
              style={{
                width: '100px',
                height: '4px',
                backgroundColor: '#8b5cf6',
                borderRadius: '2px',
              }}
            />
          </div>

          {/* Bottom Branding */}
          <div
            style={{
              position: 'absolute',
              bottom: '40px',
              right: '40px',
              display: 'flex',
              alignItems: 'center',
              color: '#9ca3af',
              fontSize: '18px',
              fontFamily: 'Inter, sans-serif',
            }}
          >
            AI-Powered Innovation
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
      }
    );
  } catch (e: any) {
    console.log(`${e.message}`);
    return new Response(`Failed to generate the image`, {
      status: 500,
    });
  }
}
