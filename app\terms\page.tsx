import Link from 'next/link';
import React from 'react';

export default function TermsPage() {
  return (
    <main className="min-h-screen pt-24 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h1 className="display-large text-primary mb-6">Terms of Service</h1>
          <p className="headline-medium text-secondary">
            These terms govern your use of our AI services and platform.
          </p>
        </div>

        <div className="prose prose-lg dark:prose-invert max-w-none">
          <div className="bg-white/50 dark:bg-black/50 backdrop-blur-sm rounded-xl p-8 border border-gray-200/50 dark:border-white/10">
            <h2 className="text-2xl font-bold text-primary mb-4">Acceptance of Terms</h2>
            <p className="text-secondary mb-6">
              By accessing and using our AI services, you accept and agree to be bound by the terms 
              and provision of this agreement.
            </p>

            <h2 className="text-2xl font-bold text-primary mb-4">Use License</h2>
            <p className="text-secondary mb-6">
              Permission is granted to temporarily use our AI services for personal, non-commercial 
              transitory viewing only under the terms of this license.
            </p>

            <h2 className="text-2xl font-bold text-primary mb-4">Service Availability</h2>
            <p className="text-secondary mb-6">
              We strive to maintain high availability of our AI services but cannot guarantee 
              uninterrupted access at all times.
            </p>

            <h2 className="text-2xl font-bold text-primary mb-4">Contact Information</h2>
            <p className="text-secondary">
              For questions about these Terms of Service, contact us at{` `}
              <Link href={`/contact`} className='text-purple-500 dark:text-purple-400 hover:underline'>here</Link>
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
