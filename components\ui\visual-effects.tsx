'use client';

import React, { useEffect, useRef } from 'react';

interface VisualEffectsProps {
  className?: string;
  effects?: Array<'particles' | 'neural' | 'grid' | 'glow' | 'waves'>;
  intensity?: 'low' | 'medium' | 'high';
}

export const VisualEffects: React.FC<VisualEffectsProps> = ({
  className = '',
  effects = ['grid', 'particles'],
  intensity = 'medium'
}) => {
  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {effects.includes('grid') && <GridEffect intensity={intensity} />}
      {effects.includes('particles') && <ParticleEffect intensity={intensity} />}
      {effects.includes('neural') && <NeuralEffect intensity={intensity} />}
      {effects.includes('glow') && <GlowEffect intensity={intensity} />}
      {effects.includes('waves') && <WaveEffect intensity={intensity} />}
    </div>
  );
};

// Grid Effect Component
interface EffectProps {
  intensity: 'low' | 'medium' | 'high';
}

const GridEffect: React.FC<EffectProps> = ({ intensity }) => {
  const intensityMap = {
    low: 0.02,
    medium: 0.04,
    high: 0.06
  };

  return (
    <div
      className="absolute inset-0"
      style={{
        backgroundImage: `
          linear-gradient(rgba(138, 43, 226, ${intensityMap[intensity]}) 1px, transparent 1px),
          linear-gradient(90deg, rgba(138, 43, 226, ${intensityMap[intensity]}) 1px, transparent 1px)
        `,
        backgroundSize: '24px 24px',
        animation: 'gridPulse 4s ease-in-out infinite'
      }}
    />
  );
};

// Particle Effect Component
const ParticleEffect: React.FC<EffectProps> = ({ intensity }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const particleCount = intensity === 'low' ? 15 : intensity === 'medium' ? 25 : 40;
    
    // Clear existing particles
    container.innerHTML = '';

    // Create particles
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-1 h-1 bg-purple-500 rounded-full opacity-30';
      
      // Random position
      particle.style.left = `${Math.random() * 100}%`;
      particle.style.top = `${Math.random() * 100}%`;
      
      // Random animation duration and delay
      const duration = 8000 + Math.random() * 12000;
      const delay = Math.random() * 5000;
      
      particle.style.animation = `floatParticle ${duration}ms linear infinite`;
      particle.style.animationDelay = `${delay}ms`;
      
      container.appendChild(particle);
    }
  }, [intensity]);

  return <div ref={containerRef} className="absolute inset-0" />;
};

// Neural Network Effect Component
const NeuralEffect: React.FC<EffectProps> = ({ intensity }) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current) return;

    const svg = svgRef.current;
    const nodeCount = intensity === 'low' ? 20 : intensity === 'medium' ? 35 : 50;
    
    // Clear existing content
    svg.innerHTML = '';

    const nodes: Array<{ x: number; y: number }> = [];

    // Create nodes
    for (let i = 0; i < nodeCount; i++) {
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', `${x}%`);
      circle.setAttribute('cy', `${y}%`);
      circle.setAttribute('r', '2');
      circle.setAttribute('fill', 'rgba(138, 43, 226, 0.6)');
      circle.setAttribute('class', 'animate-pulse');
      
      svg.appendChild(circle);
      nodes.push({ x, y });
    }

    // Create connections
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const distance = Math.sqrt(
          Math.pow(nodes[i].x - nodes[j].x, 2) + 
          Math.pow(nodes[i].y - nodes[j].y, 2)
        );
        
        if (distance < 25) {
          const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
          line.setAttribute('x1', `${nodes[i].x}%`);
          line.setAttribute('y1', `${nodes[i].y}%`);
          line.setAttribute('x2', `${nodes[j].x}%`);
          line.setAttribute('y2', `${nodes[j].y}%`);
          line.setAttribute('stroke', 'rgba(138, 43, 226, 0.3)');
          line.setAttribute('stroke-width', '1');
          line.setAttribute('class', 'animate-pulse');
          
          svg.appendChild(line);
        }
      }
    }
  }, [intensity]);

  return (
    <svg
      ref={svgRef}
      className="absolute inset-0 w-full h-full"
      viewBox="0 0 100 100"
      preserveAspectRatio="xMidYMid slice"
    />
  );
};

// Glow Effect Component
const GlowEffect: React.FC<EffectProps> = ({ intensity }) => {
  const intensityMap = {
    low: 0.1,
    medium: 0.2,
    high: 0.3
  };

  return (
    <div
      className="absolute inset-0"
      style={{
        background: `
          radial-gradient(circle at 20% 50%, rgba(138, 43, 226, ${intensityMap[intensity]}) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(138, 43, 226, ${intensityMap[intensity] * 0.7}) 0%, transparent 50%),
          radial-gradient(circle at 40% 80%, rgba(138, 43, 226, ${intensityMap[intensity] * 0.5}) 0%, transparent 50%)
        `,
        animation: 'glowPulse 6s ease-in-out infinite'
      }}
    />
  );
};

// Wave Effect Component
const WaveEffect: React.FC<EffectProps> = ({ intensity }) => {
  const intensityMap = {
    low: 0.02,
    medium: 0.04,
    high: 0.06
  };

  return (
    <div
      className="absolute inset-0"
      style={{
        backgroundImage: `
          repeating-linear-gradient(
            45deg,
            transparent,
            transparent 20px,
            rgba(138, 43, 226, ${intensityMap[intensity]}) 20px,
            rgba(138, 43, 226, ${intensityMap[intensity]}) 21px
          )
        `,
        animation: 'waveMove 8s linear infinite'
      }}
    />
  );
};

// Animated Background Component for specific sections
interface AnimatedBackgroundProps {
  className?: string;
  variant?: 'hero' | 'section' | 'card' | 'cta';
  children?: React.ReactNode;
}

export const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  className = '',
  variant = 'section',
  children
}) => {
  const getEffectsForVariant = (): Array<'particles' | 'neural' | 'grid' | 'glow' | 'waves'> => {
    switch (variant) {
      case 'hero':
        return ['grid', 'particles', 'neural', 'glow'];
      case 'section':
        return ['grid', 'particles'];
      case 'card':
        return ['glow'];
      case 'cta':
        return ['waves', 'glow'];
      default:
        return ['grid'];
    }
  };

  const getIntensityForVariant = () => {
    switch (variant) {
      case 'hero':
        return 'high' as const;
      case 'cta':
        return 'medium' as const;
      default:
        return 'low' as const;
    }
  };

  return (
    <div className={`relative ${className}`}>
      <VisualEffects 
        effects={getEffectsForVariant()}
        intensity={getIntensityForVariant()}
      />
      <div className="relative z-10">
        {children}
      </div>
      
      {/* Global styles for animations */}
      <style jsx>{`
        @keyframes gridPulse {
          0%, 100% { opacity: 0.5; }
          50% { opacity: 1; }
        }
        
        @keyframes glowPulse {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 0.6; }
        }
        
        @keyframes waveMove {
          0% { background-position: 0 0; }
          100% { background-position: 40px 40px; }
        }
        
        @keyframes floatParticle {
          0% {
            transform: translateY(100vh) translateX(0px);
            opacity: 0;
          }
          10% {
            opacity: 0.3;
          }
          90% {
            opacity: 0.3;
          }
          100% {
            transform: translateY(-100px) translateX(${Math.random() * 200 - 100}px);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
};

export default VisualEffects;
