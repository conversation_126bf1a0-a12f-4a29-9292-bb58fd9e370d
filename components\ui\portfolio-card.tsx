'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface PortfolioCardProps {
  title: string;
  description: string;
  url?: string;
  imageUrl?: string;
  tags?: string[];
  className?: string;
  fallbackIcon?: React.ReactNode;
  style?: React.CSSProperties;
}

type LoadingState = 'loading' | 'success' | 'fallback' | 'error';

export const PortfolioCard: React.FC<PortfolioCardProps> = ({
  title,
  description,
  url,
  imageUrl,
  tags = [],
  className = '',
  fallbackIcon,
  style
}) => {
  const [loadingState, setLoadingState] = useState<LoadingState>('loading');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const loadPreview = async () => {
      if (imageUrl) {
        // If we have a direct image URL, use it
        setPreviewUrl(imageUrl);
        setLoadingState('success');
        return;
      }

      if (url) {
        try {
          // Simulate website screenshot loading
          // In a real implementation, you'd use a service like:
          // - Puppeteer/Playwright for screenshots
          // - Third-party services like ScreenshotAPI
          // - Or a custom API endpoint
          
          await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
          
          // Simulate success/failure
          const success = Math.random() > 0.3; // 70% success rate
          
          if (success) {
            // In real implementation, this would be the actual screenshot URL
            setPreviewUrl(`https://via.placeholder.com/400x225/8A2BE2/FFFFFF?text=${encodeURIComponent(title)}`);
            setLoadingState('success');
          } else {
            setLoadingState('fallback');
          }
        } catch {
          setLoadingState('error');
        }
      } else {
        setLoadingState('fallback');
      }
    };

    loadPreview();
  }, [url, imageUrl, title]);

  // Card hover animation
  useEffect(() => {
    const card = cardRef.current;
    if (!card) return;

    const handleMouseEnter = () => {
      card.style.transform = 'translateY(-4px)';
      card.style.boxShadow = '0 20px 60px rgba(138, 43, 226, 0.15)';
    };

    const handleMouseLeave = () => {
      card.style.transform = 'translateY(0px)';
      card.style.boxShadow = 'var(--shadow-md)';
    };

    card.addEventListener('mouseenter', handleMouseEnter);
    card.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      card.removeEventListener('mouseenter', handleMouseEnter);
      card.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  const renderPreviewArea = () => {
    switch (loadingState) {
      case 'loading':
        return (
          <div className="w-full h-full bg-gray-200 dark:bg-gray-700 animate-pulse flex items-center justify-center">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-8 h-8 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
              <span className="body-small text-tertiary">Loading preview...</span>
            </div>
          </div>
        );
      
      case 'success':
        return (
          <div className="relative w-full h-full group overflow-hidden">
            <Image
              src={previewUrl!}
              alt={`${title} preview`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
            {/* Hover overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="bg-white/90 dark:bg-gray-900/90 rounded-full p-3">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'fallback':
      case 'error':
        return (
          <div className="w-full h-full bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-3 text-purple-600 dark:text-purple-400">
                {fallbackIcon || (
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                  </svg>
                )}
              </div>
              <span className="body-small text-tertiary">{title}</span>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div
      ref={cardRef}
      className={`card card-glow overflow-hidden ${className}`}
      style={style}
    >
      {/* Preview Area - 16:9 aspect ratio */}
      <div className="relative w-full aspect-video">
        {renderPreviewArea()}
      </div>

      {/* Content Area */}
      <div className="p-6">
        <div className="flex items-start justify-between mb-3">
          <h3 className="headline-medium text-primary font-semibold flex-1 mr-4">
            {title}
          </h3>
          {url && (
            <Link
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex-shrink-0 p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors duration-200"
              aria-label={`Visit ${title}`}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </Link>
          )}
        </div>

        <p className="body-regular text-secondary mb-4 line-clamp-2">
          {description}
        </p>

        {/* Tags */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {tags.map((tag, index) => (
              <span
                key={index}
                className="px-3 py-1 body-small font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

interface PortfolioGridProps {
  projects: Array<{
    title: string;
    description: string;
    url?: string;
    imageUrl?: string;
    tags?: string[];
    fallbackIcon?: React.ReactNode;
  }>;
  className?: string;
}

export const PortfolioGrid: React.FC<PortfolioGridProps> = ({
  projects,
  className = ''
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 ${className}`}>
      {projects.map((project, index) => (
        <PortfolioCard
          key={index}
          title={project.title}
          description={project.description}
          url={project.url}
          imageUrl={project.imageUrl}
          tags={project.tags}
          fallbackIcon={project.fallbackIcon}
          className="animate-fade-up"
          style={{ animationDelay: `${index * 150}ms` } as React.CSSProperties}
        />
      ))}
    </div>
  );
};

export default PortfolioCard;
