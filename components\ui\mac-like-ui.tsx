'use client';

import React, { useEffect, useState } from 'react';

export const MacLikeUI: React.FC = () => {
  const [animatedValues, setAnimatedValues] = useState({
    revenue: 0,
    growth: 0,
    efficiency: 0
  });

  // Animate values on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedValues({
        revenue: 847,
        growth: 234,
        efficiency: 89
      });
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Revenue data for the chart
  const revenueData = [
    { month: 'Jan', before: 120, after: 180 },
    { month: 'Feb', before: 135, after: 220 },
    { month: 'Mar', before: 140, after: 280 },
    { month: 'Apr', before: 155, after: 350 },
    { month: 'May', before: 160, after: 420 },
    { month: 'Jun', before: 170, after: 520 }
  ];

  const maxValue = Math.max(...revenueData.map(d => Math.max(d.before, d.after)));

  return (
    <div className="relative">
      {/* Mac Window Frame with Enhanced Glow */}
      <div className="relative bg-white/90 dark:bg-black/90 backdrop-blur-xl rounded-xl border border-purple-500/30 dark:border-purple-400/30 overflow-hidden shadow-2xl">
        {/* Enhanced Scale.ai style glow effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-blue-500/10 rounded-xl"></div>
        {/* Outer glow */}
        <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/20 via-blue-600/20 to-purple-600/20 rounded-xl blur-sm -z-10"></div>
        {/* Mac Window Header */}
        <div className="relative bg-gray-50/80 dark:bg-gray-900/80 backdrop-blur-sm px-4 py-3 border-b border-purple-500/20 dark:border-purple-400/20">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full shadow-sm hover:bg-red-400 transition-colors"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full shadow-sm hover:bg-yellow-400 transition-colors"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full shadow-sm hover:bg-green-400 transition-colors"></div>
            <div className="ml-4 text-sm text-gray-700 dark:text-gray-300 font-medium">
              Revenue Analytics Dashboard
            </div>
          </div>
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-purple-500/30 to-transparent"></div>
        </div>

        {/* Dashboard Content */}
        <div className="relative p-6 space-y-6 bg-white/50 dark:bg-black/50 backdrop-blur-sm border-t border-purple-500/10">
          {/* Header Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                ${animatedValues.revenue}K
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Monthly Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                +{animatedValues.growth}%
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Growth Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {animatedValues.efficiency}%
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">Efficiency</div>
            </div>
          </div>

          {/* Revenue Chart */}
          <div className="bg-gray-50/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/30 dark:border-white/10">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                Revenue Growth Comparison
              </h3>
              <div className="flex items-center space-x-4 text-xs">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                  <span className="text-gray-600 dark:text-gray-400">Before AI</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-600 dark:text-gray-400">With AI</span>
                </div>
              </div>
            </div>

            {/* Chart Area */}
            <div className="relative h-32">
              <svg className="w-full h-full" viewBox="0 0 300 120">
                {/* Grid lines */}
                {[0, 1, 2, 3, 4].map(i => (
                  <line
                    key={i}
                    x1="0"
                    y1={i * 24 + 10}
                    x2="300"
                    y2={i * 24 + 10}
                    stroke="currentColor"
                    strokeWidth="0.5"
                    className="text-gray-300 dark:text-gray-600"
                    opacity="0.5"
                  />
                ))}

                {/* Before AI Line */}
                <polyline
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  className="text-gray-400"
                  points={revenueData.map((d, i) => 
                    `${i * 50 + 25},${110 - (d.before / maxValue) * 80}`
                  ).join(' ')}
                />

                {/* With AI Line */}
                <polyline
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="3"
                  className="text-purple-500"
                  points={revenueData.map((d, i) => 
                    `${i * 50 + 25},${110 - (d.after / maxValue) * 80}`
                  ).join(' ')}
                />

                {/* Data points */}
                {revenueData.map((d, i) => (
                  <g key={i}>
                    {/* Before AI point */}
                    <circle
                      cx={i * 50 + 25}
                      cy={110 - (d.before / maxValue) * 80}
                      r="3"
                      fill="currentColor"
                      className="text-gray-400"
                    />
                    {/* With AI point */}
                    <circle
                      cx={i * 50 + 25}
                      cy={110 - (d.after / maxValue) * 80}
                      r="4"
                      fill="currentColor"
                      className="text-purple-500"
                    />
                  </g>
                ))}

                {/* Month labels */}
                {revenueData.map((d, i) => (
                  <text
                    key={i}
                    x={i * 50 + 25}
                    y="125"
                    textAnchor="middle"
                    className="text-xs fill-gray-500 dark:fill-gray-400"
                  >
                    {d.month}
                  </text>
                ))}
              </svg>
            </div>
          </div>

          {/* Bottom Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg p-3">
              <div className="text-sm font-medium text-purple-800 dark:text-purple-200">
                AI Impact
              </div>
              <div className="text-lg font-bold text-purple-900 dark:text-purple-100">
                3.2x ROI
              </div>
            </div>
            <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg p-3">
              <div className="text-sm font-medium text-green-800 dark:text-green-200">
                Time Saved
              </div>
              <div className="text-lg font-bold text-green-900 dark:text-green-100">
                40+ hrs/week
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scale.ai style subtle glow and shadow */}
      <div className="absolute inset-0 -z-10 bg-gradient-to-br from-purple-500/20 via-transparent to-blue-500/20 rounded-xl blur-2xl transform scale-110"></div>
      <div className="absolute inset-0 -z-20 bg-black/5 dark:bg-white/5 rounded-xl blur-3xl transform scale-125"></div>
    </div>
  );
};
