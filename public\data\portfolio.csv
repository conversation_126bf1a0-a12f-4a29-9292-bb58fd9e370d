project_id,title,description,domain,start_date,end_date,delivery_time_days,technologies,complexity_level,year,project_url
1,"Local Restaurant Website","Simple website with menu display and contact information","Food & Beverage",2020-01-15,2020-03-05,50,"HTML, CSS, JavaScript, PHP","Low",2020,"#"
2,"Small Business Portfolio","Professional portfolio website for local photographer","Creative Services",2020-02-10,2020-04-15,65,"WordPress, Custom CSS, jQuery","Low",2020,"#"
3,"Clinic Appointment System","Basic patient appointment booking system","Healthcare",2020-03-01,2020-05-30,90,"PHP, MySQL, Bootstrap","Medium",2020,"#"
4,"Property Listings Site","Real estate listings with basic search functionality","Real Estate",2020-04-01,2020-07-15,105,"Laravel, MySQL, jQuery","Medium",2020,"#"
5,"Inventory Tracker","Simple stock tracking system for retail store","Retail",2020-05-15,2020-08-30,107,"Java, SQLite, JavaFX","Medium",2020,"#"
6,"School Management Portal","Student enrollment and grade management","Education",2020-06-01,2020-10-15,136,"ASP.NET, SQL Server","High",2020,"#"
7,"Portfolio Website","Creative portfolio with image gallery","Creative Services",2020-07-10,2020-09-20,72,"React, Node.js","Low",2020,"#"
8,"News Blog Platform","Content management system for local news","Media",2020-08-15,2020-11-30,107,"WordPress, Custom PHP","Medium",2020,"#"
9,"Fitness Center System","Member registration and class scheduling","Fitness",2020-09-01,2020-12-15,105,"Python, Django, PostgreSQL","Medium",2020,"#"
10,"Travel Booking Platform","Tour package management and booking system","Travel & Tourism",2020-10-01,2021-01-31,122,"MEAN Stack, Stripe API","High",2020,"#"
11,"Basic E-commerce Store","Online store with product catalog and cart","E-commerce",2020-11-05,2021-02-28,115,"WooCommerce, WordPress","High",2020,"#"
12,"Salon Booking System","Appointment scheduling for beauty salon","Beauty & Wellness",2020-12-01,2021-03-15,104,"Vue.js, Express, MySQL","Medium",2020,"#"
13,"Corporate Website","Professional website for consulting firm","Business Services",2021-01-10,2021-03-25,74,"React, Node.js","Low",2021,"#"
14,"Online Tutoring Platform","Tutoring scheduling with video integration","Education Services",2021-02-01,2021-06-15,134,"Next.js, WebRTC, Prisma","High",2021,"#"
15,"Event Management System","Wedding and event planning coordination","Event Management",2021-03-15,2021-07-30,137,"Laravel, Vue.js, MySQL","High",2021,"#"
16,"Pharmacy Management","Medicine inventory and prescription tracking","Healthcare",2021-04-01,2021-08-15,136,"Django, React, PostgreSQL","High",2021,"#"
17,"CRM for Small Business","Customer relationship management system","Business Services",2021-05-10,2021-09-25,138,"React, Express, MongoDB","High",2021,"#"
18,"Food Delivery App","Local restaurant delivery platform","Food Delivery",2021-06-15,2021-10-30,137,"React Native, Node.js","High",2021,"#"
19,"Accounting Software","Small business invoicing and accounting","Financial Services",2021-07-01,2021-11-15,137,"Angular, Spring Boot, MySQL","High",2021,"#"
20,"Learning Management System","Online course platform with progress tracking","Education Technology",2021-08-10,2021-12-25,137,"Next.js, Prisma, PostgreSQL","High",2021,"#"
21,"Property Management App","Rental property and tenant management","Property Management",2021-09-05,2022-01-20,137,"React Native, Express, MongoDB","High",2021,"#"
22,"Healthcare Appointments","Multi-clinic scheduling and patient records","Healthcare",2021-10-15,2022-02-28,136,"React, Firebase, Stripe","High",2021,"#"
23,"Logistics Tracking System","Warehouse and delivery tracking","Logistics",2021-11-01,2022-03-15,134,"Flutter, Node.js, MySQL","High",2021,"#"
24,"Car Rental Platform","Vehicle rental with tracking and management","Transportation",2021-12-10,2022-04-25,136,"Laravel, Vue.js, MySQL","High",2021,"#"
25,"Social Media Dashboard","Brand monitoring and analytics platform","Marketing Technology",2022-01-15,2022-06-30,166,"React, Python, MongoDB","High",2022,"#"
26,"Manufacturing ERP","Production planning and inventory management","Manufacturing",2022-02-10,2022-07-25,165,"Spring Boot, Angular, Oracle","High",2022,"#"
27,"Supply Chain Platform","Multi-vendor coordination and tracking","Supply Chain",2022-03-01,2022-08-15,167,"Microservices, React, Kafka","High",2022,"#"
28,"Digital Banking Dashboard","Secure banking interface and transactions","Banking",2022-04-15,2022-09-30,168,"React Native, Node.js, MongoDB","High",2022,"#"
29,"Customer Service Portal","Multi-channel customer support system","Customer Service",2022-05-01,2022-10-15,167,"React, Python, REST APIs","High",2022,"#"
30,"IoT Monitoring Dashboard","Real-time sensor data visualization","IoT & Automation",2022-06-10,2022-11-25,168,"React, Python, InfluxDB","High",2022,"#"
31,"Document Management System","Legal document storage and search","Legal Services",2022-07-01,2022-12-20,172,"ASP.NET Core, React, SQL Server","High",2022,"#"
32,"HR Management Suite","Employee management with payroll integration","Human Resources",2022-08-15,2023-01-30,168,"Vue.js, Express, PostgreSQL","High",2022,"#"
33,"Fleet Management System","Vehicle tracking with maintenance scheduling","Transportation",2022-09-01,2023-02-15,167,"React, Node.js, MongoDB","High",2022,"#"
34,"Multi-vendor Marketplace","E-commerce platform with vendor management","E-commerce",2022-10-10,2023-03-25,166,"Next.js, Prisma, Stripe","High",2022,"#"
35,"Telemedicine Platform","Virtual healthcare consultation system","Healthcare Technology",2022-11-05,2023-04-20,166,"React, WebRTC, Firebase","High",2022,"#"
36,"Energy Monitoring System","Smart building energy usage tracking","Energy Management",2022-12-01,2023-05-15,165,"React, IoT APIs, Time Series DB","High",2022,"#"
37,"Retail Analytics Platform","Customer behavior analysis dashboard","Retail Technology",2023-01-15,2023-05-30,135,"Vue.js, Python, BigQuery","High",2023,"#"
38,"Municipal Services Portal","Citizen services and complaint management","Government",2023-02-01,2023-06-20,139,"React, Node.js, PostgreSQL","High",2023,"#"
39,"P2P Lending Platform","Peer-to-peer lending with risk management","Financial Technology",2023-03-10,2023-07-25,137,"Next.js, Python, ML APIs","High",2023,"#"
40,"Student Assessment Platform","Educational assessment and grading system","Education Technology",2023-04-05,2023-08-20,137,"React, Python, REST APIs","High",2023,"#"
41,"Inventory Forecasting System","Predictive inventory management for retailers","Retail Technology",2023-05-15,2023-09-30,138,"Vue.js, Python, Analytics APIs","High",2023,"#"
42,"Content Management Platform","Advanced CMS for marketing agencies","Marketing Technology",2023-06-01,2023-10-15,136,"Next.js, MongoDB, REST APIs","High",2023,"#"
43,"Quality Control System","Manufacturing inspection and reporting","Manufacturing",2023-07-10,2023-11-25,138,"Python, React, Computer Vision APIs","High",2023,"#"
44,"Social Media Analytics","Brand sentiment monitoring and reporting","Social Media",2023-08-15,2023-12-30,137,"React, Python, NLP APIs","High",2023,"#"
45,"Smart Parking System","Parking space monitoring and booking","Smart City",2023-09-01,2024-01-15,136,"React, IoT APIs, Real-time DB","High",2023,"#"
46,"Recruitment Platform","Candidate screening and matching system","Human Resources",2023-10-10,2024-02-25,138,"Next.js, Python, REST APIs","High",2023,"#"
47,"E-learning Analytics","Student performance tracking and insights","Education Technology",2023-11-05,2024-03-20,136,"React, Python, Analytics APIs","High",2023,"#"
48,"Supply Chain Optimization","Logistics and delivery optimization platform","Supply Chain",2023-12-01,2024-04-15,136,"React, Python, Route APIs","High",2023,"#"
49,"Corporate Website Redesign","Modern responsive website for tech company","Business Services",2024-01-15,2024-02-15,31,"Next.js, Tailwind CSS","Medium",2024,"#"
50,"E-commerce Mobile App","Shopping app with modern UX","E-commerce",2024-02-01,2024-03-20,48,"React Native, Node.js","High",2024,"#"
51,"Content Management System","Headless CMS for publishing company","Content Management",2024-03-10,2024-04-25,46,"Next.js, Strapi, PostgreSQL","High",2024,"#"
52,"Inventory Management App","Modern inventory system with real-time updates","Retail Technology",2024-04-05,2024-05-20,45,"React, Python, WebSockets","High",2024,"#"
53,"Voice-Enabled Website","Website with voice navigation features","Web Development",2024-05-15,2024-07-05,51,"React Native, Speech APIs","High",2024,"#"
54,"Maintenance Dashboard","Equipment monitoring with predictive alerts","Industrial IoT",2024-06-01,2024-07-20,49,"React, Python, IoT APIs","High",2024,"#"
55,"Sales CRM Platform","Modern sales automation platform","Sales Technology",2024-07-10,2024-08-30,51,"Next.js, Python, CRM APIs","High",2024,"#"
56,"Document Processing Platform","Automated document workflows","Document Management",2024-08-15,2024-10-05,51,"React, Python, OCR APIs","High",2024,"#"
57,"Healthcare Management System","Patient management with appointment scheduling","Healthcare Technology",2024-09-01,2024-10-25,54,"React, Python, Medical APIs","High",2024,"#"
58,"Personalized Learning Platform","Adaptive learning management system","Education Technology",2024-10-10,2024-12-05,56,"Next.js, Python, Education APIs","High",2024,"#"
59,"Security Monitoring System","Real-time security and fraud detection","Security Technology",2024-11-01,2024-12-25,54,"React, Python, Security APIs","High",2024,"#"
60,"Bohana CMO","Partnered with us to implement neuroscience and marketing strategies to improve their efficiency by leveraging the AI tools and agents","Business Automation",2024-12-05,2025-01-30,56,"React, Marketing APIs, CRM, Agentic Workflows","High",2024,"#"
61,"Dukancard","A revolutionary platform allowing local businesses to setup their online store and build a local community in the platform by offering their services to customers","Web/App Development",2025-01-10,2025-07-15,36,"Next.js, Express.Js, Python, LLM Integration","High",2025,"https://dukancard.in"
62,"Shreevimals - Texas, USA","Business Automation which improved the efficiency of the company","Business Automation",2025-02-15,2025-03-25,38,"React, Python, Marketing APIs","High",2025,"#"
63,"Uniform Uncle - E2E Ecommerce Store for Uniforms Industry","A complete e-commerce platform dealing with uniforms in India across all sectors from school to security to traditional uniforms","E-Commerce Store",2025-03-20,2025-05-05,46,"Next.js, Shopify, CRM Integration, Razorpay","High",2025,"https://uniformuncle.in"
64,"C-Suite - Workforce of AI Agentic Executives","AI Agentic executives & employees working 24x7 for the company","Business Automation",2025-04-25,2025-06-10,46,"Next Js, Python, Gemini 2.5 Flash/Pro","High",2025,"#"
65,"Lugdi - Complete Fashion Ecommerce Store","Complete E2E Fashion Ecommerce Store with Admin Dashboard & Payment Gateway Integration","Ecommerce Store",2025-05-30,2025-07-15,46,"Next.Js, Shopify, Google Analytics","High",2025,"#"
66,"Kanoon AI - AI Law Assistant","AI Law Chat Assistant & Judge","AI Chat Platform",2025-06-15,2025-08-05,51,"Next.js, LLM, Gemini 2.5 Pro","High",2025,"#"
67,"Restaurant POS System","Point of sale system with inventory management","Food & Beverage",2020-03-20,2020-05-30,71,"PHP, MySQL, jQuery","Medium",2020,"#"
68,"Real Estate CRM","Customer relationship management for realtors","Real Estate",2020-07-01,2020-09-15,76,"Laravel, Vue.js, MySQL","Medium",2020,"#"
69,"Gym Management System","Member management and billing system","Fitness",2021-01-20,2021-04-10,80,"Django, React, PostgreSQL","Medium",2021,"#"
70,"Veterinary Clinic System","Pet and appointment management system","Healthcare",2021-05-01,2021-07-20,80,"ASP.NET, SQL Server","Medium",2021,"#"
71,"Nonprofit Donation Platform","Online donation system with donor management","Nonprofit",2021-08-01,2021-10-15,75,"WordPress, WooCommerce","Medium",2021,"#"
72,"Legal Case Management","Law firm case and document management","Legal Services",2022-01-10,2022-04-15,95,"React, Node.js, MongoDB","High",2022,"#"
73,"Construction Project Manager","Project tracking and resource management","Construction",2022-06-01,2022-09-10,101,"Angular, Spring Boot, MySQL","High",2022,"#"
74,"Hotel Management System","Reservation and guest management platform","Hospitality",2023-02-15,2023-05-25,99,"Next.js, Prisma, PostgreSQL","High",2023,"#"
75,"Freelancer Marketplace","Platform connecting freelancers with clients","Marketplace",2023-09-10,2023-12-20,101,"React, Node.js, Stripe","High",2023,"#"