import React from 'react';

export default function CookiesPage() {
  return (
    <main className="min-h-screen pt-24 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h1 className="display-large text-primary mb-6">Cookie Policy</h1>
          <p className="headline-medium text-secondary">
            Learn how we use cookies to improve your experience on our platform.
          </p>
        </div>

        <div className="prose prose-lg dark:prose-invert max-w-none">
          <div className="bg-white/50 dark:bg-black/50 backdrop-blur-sm rounded-xl p-8 border border-gray-200/50 dark:border-white/10">
            <h2 className="text-2xl font-bold text-primary mb-4">What Are Cookies</h2>
            <p className="text-secondary mb-6">
              Cookies are small text files that are stored on your device when you visit our website. 
              They help us provide you with a better experience.
            </p>

            <h2 className="text-2xl font-bold text-primary mb-4">How We Use Cookies</h2>
            <p className="text-secondary mb-6">
              We use cookies to remember your preferences, analyze site traffic, and improve our 
              AI services based on user behavior patterns.
            </p>

            <h2 className="text-2xl font-bold text-primary mb-4">Managing Cookies</h2>
            <p className="text-secondary mb-6">
              You can control and/or delete cookies as you wish through your browser settings. 
              However, this may affect the functionality of our services.
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
