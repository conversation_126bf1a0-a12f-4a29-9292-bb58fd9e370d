'use client';

import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';
import { ScaleCard } from '../../components/ui/scale-card';
import Image from 'next/image';
import { <PERSON>rkles, Award, Users, Target } from 'lucide-react';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

export default function FoundersPage() {
  const foundersRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate founder cards on scroll
      gsap.fromTo('.founder-card',
        {
          y: 100,
          opacity: 0,
          rotationY: -15,
          scale: 0.9
        },
        {
          y: 0,
          opacity: 1,
          rotationY: 0,
          scale: 1,
          duration: 1.2,
          ease: 'power3.out',
          stagger: 0.3,
          scrollTrigger: {
            trigger: '.founders-section',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Animate stats cards
      gsap.fromTo('.stats-card',
        {
          y: 50,
          opacity: 0,
          scale: 0.8
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          ease: 'back.out(1.7)',
          stagger: 0.1,
          scrollTrigger: {
            trigger: '.stats-section',
            start: 'top 85%',
            toggleActions: 'play none none reverse'
          }
        }
      );

      // Floating animation for founder images
      gsap.to('.founder-image', {
        y: '10px',
        duration: 3,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true,
        stagger: 0.5
      });

    }, foundersRef);

    return () => ctx.revert();
  }, []);
  const founders = [
    {
      name: "Zunaid Ahaal",
      role: "Co-Founder & CEO",
      department: "Business, Marketing & Operations",
      image: "/Zunaid Ahaal.webp",
      description: "Driving business growth and operational excellence while building lasting client relationships.",
      expertise: [
        "Business Strategy & Growth",
        "Marketing & Brand Development",
        "Operations Management",
        "Client Relationship Management"
      ],
      achievements: [
        "Scaled company to 100+ projects",
        "Built strategic partnerships",
        "Developed market-leading solutions",
        "Achieved 95% client satisfaction"
      ]
    },
    {
      name: "Marshal Tudu",
      role: "Co-Founder & CTO",
      department: "Technical & Project Management",
      image: "/Marshal Tudu.webp",
      description: "Leading our technical vision and ensuring seamless project delivery across all AI implementations.",
      expertise: [
        "AI Architecture & Development",
        "Project Management Excellence", 
        "Technical Strategy & Innovation",
        "Team Leadership & Mentoring"
      ],
      achievements: [
        "Led 100+ successful AI projects",
        "Built scalable AI infrastructure",
        "Mentored 20+ technical professionals",
        "Pioneered innovative AI solutions"
      ]
    }
  ];

  const companyStats = [
    {
      number: "5+",
      label: "Years of Excellence",
      description: "Delivering cutting-edge AI solutions"
    },
    {
      number: "100+",
      label: "Projects Completed",
      description: "Successful implementations across industries"
    },
    {
      number: "95%",
      label: "Client Satisfaction",
      description: "Consistently exceeding expectations"
    },
    {
      number: "50+",
      label: "Enterprise Clients",
      description: "Trusted by leading organizations"
    }
  ];

  return (
    <main className="min-h-screen" ref={foundersRef}>
      {/* Enhanced Company Journey Section */}
      <section className="py-36 section-bg-secondary relative overflow-hidden stats-section">
        {/* Animated background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              className="display-medium text-primary mb-6 flex items-center justify-center gap-4"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, ease: 'easeOut' }}
            >
              <Award className="w-10 h-10 text-purple-600" />
              Our Journey of Innovation
              <Target className="w-10 h-10 text-purple-600" />
            </motion.h2>
            <motion.p
              className="headline-medium text-secondary max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.3, ease: 'easeOut' }}
            >
              Five years of relentless innovation, building AI solutions that transform businesses and drive measurable results across industries.
            </motion.p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {companyStats.map((stat, index) => (
              <motion.div
                key={index}
                className="stats-card"
                initial={{ opacity: 0, y: 50, scale: 0.8 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.8, delay: index * 0.1, ease: "backOut" }}
                whileHover={{ scale: 1.05, y: -5 }}
              >
                <ScaleCard
                  variant="featured"
                  className="text-center h-full bg-white/90 dark:bg-black/90 backdrop-blur-xl border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300"
                >
                  <motion.div
                    className="text-4xl lg:text-5xl font-bold text-purple-600 dark:text-purple-400 mb-2"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 + 0.5, type: 'spring', stiffness: 200 }}
                  >
                    {stat.number}
                  </motion.div>
                  <h3 className="headline-large text-primary mb-2">{stat.label}</h3>
                  <p className="body-regular text-secondary">{stat.description}</p>
                </ScaleCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Founders Section */}
      <section className="py-24 section-bg-primary relative overflow-hidden founders-section">
        {/* Background effects */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-3xl animate-pulse" style={{ animationDelay: '3s' }}></div>
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <motion.h2
              className="display-medium text-primary mb-6 flex items-center justify-center gap-4"
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, ease: 'easeOut' }}
              viewport={{ once: true }}
            >
              <Users className="w-10 h-10 text-purple-600" />
              Leadership Excellence
              <Sparkles className="w-10 h-10 text-purple-600 animate-pulse" />
            </motion.h2>
            <motion.p
              className="headline-medium text-secondary max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.3, ease: 'easeOut' }}
              viewport={{ once: true }}
            >
              Meet the dynamic duo behind OfStartup.ai, combining technical expertise with business acumen to deliver exceptional AI solutions.
            </motion.p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {founders.map((founder, index) => (
              <motion.div
                key={index}
                className="founder-card"
                initial={{ opacity: 0, y: 100, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 1.2, delay: index * 0.3, ease: "easeOut" }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
              >
                <ScaleCard
                  variant="glow"
                  size="lg"
                  className="overflow-hidden bg-white/95 dark:bg-black/95 backdrop-blur-xl border border-purple-500/30 hover:border-purple-500/50 transition-all duration-500"
                >
                  {/* Enhanced Founder Image */}
                  <div className="relative mb-8">
                    <motion.div
                      className="relative w-40 h-40 mx-auto mb-6 founder-image"
                      whileHover={{ scale: 1.1, rotateY: 10 }}
                      transition={{ duration: 0.3 }}
                    >
                      {/* Multiple glow layers */}
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-500/30 to-blue-500/30 rounded-full blur-2xl animate-pulse"></div>
                      <div className="absolute inset-2 bg-gradient-to-br from-purple-400/20 to-blue-400/20 rounded-full blur-xl"></div>
                      <div className="absolute inset-4 bg-gradient-to-br from-purple-300/10 to-blue-300/10 rounded-full blur-lg"></div>

                      <motion.div
                        className="relative w-full h-full"
                        whileHover={{ rotateY: 5 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Image
                          src={founder.image}
                          alt={founder.name}
                          width={160}
                          height={160}
                          className="relative rounded-full object-cover border-4 border-white/30 dark:border-white/20 shadow-2xl"
                        />
                      </motion.div>
                    </motion.div>

                    <motion.div
                      className="text-center"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: index * 0.3 + 0.5 }}
                      viewport={{ once: true }}
                    >
                      <h3 className="display-small text-primary mb-2 flex items-center justify-center gap-2">
                        <Sparkles className="w-6 h-6 text-purple-600" />
                        {founder.name}
                      </h3>
                      <p className="headline-medium text-purple-600 dark:text-purple-400 mb-2 font-bold">{founder.role}</p>
                      <p className="body-large text-secondary font-medium bg-purple-100 dark:bg-purple-900/30 px-4 py-2 rounded-full inline-block">{founder.department}</p>
                    </motion.div>
                  </div>

                {/* Description */}
                <p className="body-large text-secondary mb-6 text-center">
                  {founder.description}
                </p>

                {/* Expertise */}
                <div className="mb-6">
                  <h4 className="headline-medium text-primary mb-3">Core Expertise</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {founder.expertise.map((skill, skillIndex) => (
                      <div key={skillIndex} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span className="body-regular text-secondary">{skill}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Achievements */}
                <div>
                  <h4 className="headline-medium text-primary mb-3">Key Achievements</h4>
                  <div className="space-y-2">
                    {founder.achievements.map((achievement, achievementIndex) => (
                      <div key={achievementIndex} className="flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="body-regular text-secondary">{achievement}</span>
                      </div>
                    ))}
                  </div>
                </div>
                </ScaleCard>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Vision & Mission Section */}
      <section className="py-24 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <ScaleCard variant="featured" size="lg">
              <div className="text-center lg:text-left">
                <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center mx-auto lg:mx-0 mb-6">
                  <svg className="w-8 h-8 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </div>
                <h3 className="display-small text-primary mb-4">Our Vision</h3>
                <p className="body-large text-secondary">
                  To democratize AI technology and make it accessible to businesses of all sizes, enabling them to harness the power of artificial intelligence for sustainable growth and innovation.
                </p>
              </div>
            </ScaleCard>

            <ScaleCard variant="featured" size="lg">
              <div className="text-center lg:text-left">
                <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center mx-auto lg:mx-0 mb-6">
                  <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="display-small text-primary mb-4">Our Mission</h3>
                <p className="body-large text-secondary">
                  To deliver cutting-edge AI solutions that solve real-world business challenges, while maintaining the highest standards of quality, innovation, and client satisfaction.
                </p>
              </div>
            </ScaleCard>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-purple-600 via-black to-purple-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="display-medium mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="headline-medium mb-10 opacity-90">
            Join the 100+ companies that have already transformed their operations with our AI solutions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="btn-primary px-8 py-4 body-large font-semibold"
            >
              Start Your AI Journey
            </a>
            <a
              href="/portfolio"
              className="px-8 py-4 body-large font-semibold border-2 border-white/30 rounded-lg hover:bg-white/10 transition-all duration-300"
            >
              View Our Work
            </a>
          </div>
        </div>
      </section>
    </main>
  );
}
