'use client';

import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger';
import { motion } from 'framer-motion';
import { Cpu, <PERSON><PERSON><PERSON>, Puzzle } from 'lucide-react';
import { MacLikeUI } from './mac-like-ui';

gsap.registerPlugin(ScrollTrigger);

interface HeroSectionProps {
  className?: string;
  backgroundType?: 'neural' | 'particles' | 'both' | 'none';
  children?: React.ReactNode;
}

export const HeroSection: React.FC<HeroSectionProps> = ({
  className = '',
  backgroundType = 'both',
  children
}) => {
  const heroRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Enhanced floating animation for features
      gsap.to('.floating-feature', {
        y: '30px',
        rotation: '5deg',
        duration: 5,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true,
        stagger: { each: 0.8, from: 'random' },
      });

      // Add subtle scale animation
      gsap.to('.floating-feature', {
        scale: 1.05,
        duration: 3,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true,
        stagger: { each: 1.2, from: 'random' },
      });
    }, heroRef);

    return () => ctx.revert();
  }, []);

  const features = [
    { title: 'AI-Powered', icon: <Cpu size={20} /> },
    { title: 'Data-Driven', icon: <BarChart size={20} /> },
    { title: 'Seamless Integration', icon: <Puzzle size={20} /> },
  ];

  return (
    <section
      ref={heroRef}
      className={`hero-section relative min-h-screen flex items-center justify-center pt-36 pb-16 md:pt-16 md:pb-0 ${className}`}
    >

      {/* Content Container */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
          {/* Hero Content */}
          <div className='flex flex-col items-center justify-center font-bold space-y-1'>
            <motion.div
              initial={{ opacity: 0, filter: 'blur(10px)', y: 50 }}
              animate={{ opacity: 1, filter: 'blur(0px)', y: 0 }}
              transition={{ duration: 1, ease: 'easeOut' }}
              className="text-center"
            >
              <h1 className='text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl text-foreground leading-tight'>
                You Imagine
              </h1>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, filter: 'blur(10px)', y: 50 }}
              animate={{ opacity: 1, filter: 'blur(0px)', y: 0 }}
              transition={{ duration: 1, delay: 0.3, ease: 'easeOut' }}
              className="text-center"
            >
              <h1 className='text-4xl sm:text-5xl md:text-6xl lg:text-8xl xl:text-9xl text-foreground leading-tight'>
                We <span className="text-purple-600 dark:text-purple-400">Build</span>
              </h1>
            </motion.div>

            {/* Tagline */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.6, ease: 'easeOut' }}
              className="text-center mt-1"
            >
              <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-secondary font-medium">
                Transform your business with AI
              </p>
            </motion.div>
          </div>

          {/* Mac-like UI with Floating Features */}
          <div className="relative max-w-4xl mx-auto w-full h-full min-h-[300px] sm:min-h-[350px] lg:min-h-[400px] xl:min-h-[500px]">
            <div className="relative opacity-0 animate-fade-up z-10" style={{ animationDelay: '400ms' }}>
              <MacLikeUI />
            </div>

            {/* Floating Features Wrapper - Responsive positioning */}
            <div className="absolute inset-0 z-20">
              <FloatingFeature
                icon={features[0].icon}
                title={features[0].title}
                className="floating-feature absolute -top-8 -left-4 sm:top-0 sm:left-0 md:-top-4 md:-left-8 lg:-left-12 xl:-left-16"
              />
              <FloatingFeature
                icon={features[1].icon}
                title={features[1].title}
                className="floating-feature absolute -bottom-8 -right-4 sm:bottom-0 sm:right-0 md:-bottom-4 md:-right-4 lg:-right-8 xl:-right-12"
              />
              <FloatingFeature
                icon={features[2].icon}
                title={features[2].title}
                className="floating-feature absolute top-1/2 -translate-y-1/2 -right-4 sm:right-0 md:-right-8 lg:-right-20 xl:-right-24"
              />
            </div>
          </div>
        </div>

        {children && <div className="mt-16">{children}</div>}
      </div>

      {/* Gradient Overlay */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{ background: 'radial-gradient(ellipse at center, transparent 0%, rgba(0,0,0,0.1) 100%)' }}
      />

            <style jsx>{`
        .hero-section-container {
          background-color: white;
        }
        .dark .hero-section-container {
          background-color: black;
        }

        @keyframes fadeUp {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-up { animation: fadeUp 0.8s ease-out forwards; }

        /* Enhanced responsive adjustments for floating features */
        @media (max-width: 640px) {
          .floating-feature {
            transform: scale(0.7);
            max-width: 100px;
          }
        }

        @media (max-width: 768px) {
          .floating-feature {
            transform: scale(0.8);
            max-width: 110px;
          }
        }

        /* Ensure floating features don't overflow on smaller screens */
        @media (max-width: 1024px) {
          .floating-feature {
            max-width: 120px;
          }
        }
      `}</style>
    </section>
  );
};

interface FloatingFeatureProps {
  icon: React.ReactNode;
  title: string;
  className?: string;
}

const FloatingFeature: React.FC<FloatingFeatureProps> = ({ icon, title, className }) => {
  return (
    <div
      className={`flex flex-col items-center justify-center text-center gap-2 bg-background/70 backdrop-blur-xl p-3 sm:p-4 rounded-xl border border-purple-500/30 shadow-xl text-foreground hover:border-purple-500/50 transition-all duration-300 hover:shadow-purple-500/20 hover:shadow-2xl ${className}`}>
      <div className="text-purple-600 dark:text-purple-400 p-1 rounded-lg bg-purple-100/50 dark:bg-purple-900/30">
        {icon}
      </div>
      <h3 className="font-bold text-xs sm:text-sm whitespace-nowrap">{title}</h3>
    </div>
  );
};