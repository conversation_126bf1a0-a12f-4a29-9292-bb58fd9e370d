import React from 'react';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact Us - Get Started with AI Solutions | ofstartup.ai',
  description: 'Ready to transform your business with AI? Contact ofstartup.ai today. Get expert AI consulting, custom solutions, and 24-hour response time. Located in Rourkela, Odisha, India.',
  keywords: [
    'contact ofstartup',
    'AI consulting contact',
    'AI solutions inquiry',
    'business transformation',
    'custom AI development',
    'Rourkela AI company',
    'AI consultation',
    'enterprise AI solutions'
  ],
  openGraph: {
    title: 'Contact ofstartup.ai - AI Solutions & Consulting',
    description: 'Get in touch with our AI experts. 24-hour response time, custom solutions, and comprehensive AI consulting services.',
    type: 'website',
    locale: 'en_US',
    url: 'https://ofstartupglobal.com/contact',
    siteName: 'ofstartup.ai',
    images: [
      {
        url: '/api/og?title=Contact%20ofstartup.ai&description=Get%20in%20touch%20with%20our%20AI%20experts.%2024-hour%20response%20time&page=contact',
        width: 1200,
        height: 630,
        alt: 'Contact ofstartup.ai for AI Solutions',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact ofstartup.ai - AI Solutions & Consulting',
    description: 'Get in touch with our AI experts. 24-hour response time and custom AI solutions.',
    images: ['/api/og?title=Contact%20ofstartup.ai&description=Get%20in%20touch%20with%20our%20AI%20experts.%2024-hour%20response%20time&page=contact'],
  },
  alternates: {
    canonical: 'https://ofstartupglobal.com/contact',
  },
};

export default function ContactPage() {
  return (
    <>
      {/* Contact Section */}
      <section id="contact" className="py-36 section-bg-secondary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Get Started Today
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Tell us about your project and we&apos;ll get back to you within 24 hours with a custom proposal.
            </p>
          </div>

          {/* Contact Information Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            {/* Email Card */}
            <div className="card p-8 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-purple-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-primary mb-2">Email</h3>
              <p className="text-secondary mb-4">Get in touch via email</p>
              <a 
                href="mailto:<EMAIL>" 
                className="text-purple-600 dark:text-purple-400 hover:underline font-medium"
              >
                <EMAIL>
              </a>
            </div>

            {/* Phone Card */}
            <div className="card p-8 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-purple-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-primary mb-2">Phone</h3>
              <p className="text-secondary mb-4">Call us directly</p>
              <div className="space-y-1">
                <a 
                  href="tel:+917325848747" 
                  className="block text-purple-600 dark:text-purple-400 hover:underline font-medium"
                >
                  +91 7325848747
                </a>
                <a 
                  href="tel:+918458060663" 
                  className="block text-purple-600 dark:text-purple-400 hover:underline font-medium"
                >
                  +91 8458060663
                </a>
              </div>
            </div>

            {/* Office Card */}
            <div className="card p-8 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-purple-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-primary mb-2">Office</h3>
              <p className="text-secondary mb-4">Visit us at</p>
              <address className="text-purple-600 dark:text-purple-400 font-medium not-italic">
                Bisra Road Near Shani Mandir<br />
                Rourkela, Odisha, India - 769043
              </address>
            </div>

            {/* Response Time Card */}
            <div className="card p-8 text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-600 to-purple-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-primary mb-2">Response Time</h3>
              <p className="text-secondary mb-4">We respond fast</p>
              <p className="text-purple-600 dark:text-purple-400 font-medium">
                Within 24 hours
              </p>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <h3 className="text-2xl font-bold text-primary mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="text-secondary mb-8 max-w-2xl mx-auto">
              Whether you need OfImpact for business intelligence, OfLogic for neuromarketing, or custom AI solutions, 
              we&apos;re here to help you succeed.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="btn-primary px-8 py-4 text-lg font-semibold"
              >
                Send us an Email
              </a>
              <a
                href="tel:+917325848747"
                className="btn-secondary px-8 py-4 text-lg font-semibold"
              >
                Call Now
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Frequently Asked Questions
            </h2>
            <p className="headline-medium text-secondary">
              Common questions about our AI services and implementation process.
            </p>
          </div>

          <div className="space-y-8">
            {[
              {
                question: "How long does an AI implementation typically take?",
                answer: "Implementation timelines vary based on project complexity, but most projects are completed within 3-6 months. We provide detailed timelines during the consultation phase."
              },
              {
                question: "Do you provide ongoing support after implementation?",
                answer: "Yes, we offer comprehensive support packages including monitoring, optimization, and updates to ensure your AI solution continues to deliver value."
              },
              {
                question: "What industries do you work with?",
                answer: "We work across all industries including healthcare, finance, e-commerce, manufacturing, and more. Our solutions are tailored to each industry's specific needs."
              },
              {
                question: "How do you ensure data security and privacy?",
                answer: "We follow industry-leading security practices including encryption, secure data handling, and compliance with regulations like GDPR and HIPAA where applicable."
              }
            ].map((faq, index) => (
              <div key={index} className="card p-8">
                <h3 className="headline-large text-primary mb-4">{faq.question}</h3>
                <p className="body-regular text-secondary">{faq.answer}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  );
}
