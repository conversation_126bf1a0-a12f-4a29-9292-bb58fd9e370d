import React from 'react';
import { HeroSection } from '../components/ui/hero-section';
import { ServiceCardGrid } from '../components/ui/service-card';
import { StatsCard, FeatureCard } from '../components/ui/custom-card';
import { Building2, ShoppingCart, Hospital } from 'lucide-react';

export default function Home() {

  // Services for the service card grid
  const services = [
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      title: "OfImpact",
      description: "Comprehensive business intelligence and analytics platform that transforms your data into actionable insights.",
      ctaText: "Explore OfImpact",
      ctaHref: "/ofimpact",
      featured: true
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
      title: "OfLogic",
      description: "Advanced neuromarketing and consumer behavior analysis powered by cutting-edge AI algorithms.",
      ctaText: "Discover OfLogic",
      ctaHref: "/oflogic",
      featured: true
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      title: "Custom Solutions",
      description: "Tailored AI solutions designed specifically for your unique business requirements and challenges.",
      ctaText: "Get Custom Solution",
      ctaHref: "/contact",
      featured: true
    }
  ];

  return (
    <>
      {/* Hero Section */}
      <HeroSection
        backgroundType="both"
      />

      {/* Our Services Section */}
      <section className="py-24 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Our AI Solutions
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Discover our comprehensive suite of AI-powered tools designed to transform your business operations.
            </p>
          </div>

          <ServiceCardGrid services={services} />
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Proven Results
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Our AI solutions have delivered measurable impact across industries.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <StatsCard
              title="Cost Reduction"
              value="40%"
              description="Average cost savings achieved"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              }
              trend={{ value: 12, isPositive: true }}
            />
            <StatsCard
              title="Efficiency Gain"
              value="3x"
              description="Faster processing times"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              trend={{ value: 25, isPositive: true }}
            />
            <StatsCard
              title="Accuracy Rate"
              value="99.2%"
              description="AI model precision"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              trend={{ value: 5, isPositive: true }}
            />
            <StatsCard
              title="Client Satisfaction"
              value="98%"
              description="Customer satisfaction rate"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              }
              trend={{ value: 8, isPositive: true }}
            />
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-24 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Why Choose Ofstartup.ai?
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              We combine cutting-edge AI technology with deep business expertise to deliver solutions that drive real results.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FeatureCard
              icon={
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              }
              title="Expert Team"
              description="World-class AI researchers and business strategists with proven track records"
            />
            <FeatureCard
              icon={
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              }
              title="Proven Results"
              description="Track record of successful AI implementations across diverse industries"
            />
            <FeatureCard
              icon={
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              }
              title="Custom Solutions"
              description="Tailored AI solutions designed specifically for your unique business requirements"
            />
            <FeatureCard
              icon={
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              }
              title="24/7 Support"
              description="Round-the-clock support and maintenance to ensure optimal performance"
            />
          </div>
        </div>
      </section>

      {/* Our Approach Section */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Our Approach
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              A systematic methodology that ensures successful AI implementation and measurable business impact.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: "01",
                title: "Analyze",
                description: "Deep dive into your business processes and identify AI opportunities"
              },
              {
                step: "02",
                title: "Design",
                description: "Create custom AI solutions tailored to your specific requirements"
              },
              {
                step: "03",
                title: "Deploy",
                description: "Seamless implementation with ongoing support and optimization"
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                  <span className="headline-large font-bold text-purple-600 dark:text-purple-400">
                    {item.step}
                  </span>
                </div>
                <h3 className="headline-large text-primary mb-4">{item.title}</h3>
                <p className="body-regular text-secondary">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Success Stories
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              See how our AI solutions have transformed businesses across industries.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                company: "TechCorp Inc.",
                industry: "Technology",
                result: "40% cost reduction",
                description: "Automated customer service with AI chatbots, reducing operational costs while improving response times.",
                logo: <Building2 className="w-8 h-8" />
              },
              {
                company: "RetailMax",
                industry: "E-commerce",
                result: "3x sales increase",
                description: "Implemented personalized recommendation engine that tripled conversion rates and customer engagement.",
                logo: <ShoppingCart className="w-8 h-8" />
              },
              {
                company: "HealthPlus",
                industry: "Healthcare",
                result: "99.2% accuracy",
                description: "Deployed diagnostic AI system that improved accuracy and reduced diagnosis time by 60%.",
                logo: <Hospital className="w-8 h-8" />
              }
            ].map((story, index) => (
              <div key={index} className="card p-8 text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center text-purple-600 dark:text-purple-400">{story.logo}</div>
                <h3 className="headline-large text-primary mb-2">{story.company}</h3>
                <p className="body-small text-tertiary uppercase tracking-wide mb-4">{story.industry}</p>
                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-4">
                  {story.result}
                </div>
                <p className="body-regular text-secondary">{story.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* AI Advantage Section */}
      <section className="py-24 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="display-medium text-primary mb-6">
                The AI Advantage
              </h2>
              <p className="headline-medium text-secondary mb-8">
                Discover how artificial intelligence can revolutionize your business operations and drive unprecedented growth.
              </p>

              <div className="space-y-6">
                {[
                  {
                    title: "Intelligent Automation",
                    description: "Automate complex processes with AI-powered workflows that adapt and learn."
                  },
                  {
                    title: "Predictive Analytics",
                    description: "Forecast trends and make data-driven decisions with advanced machine learning."
                  },
                  {
                    title: "Personalized Experiences",
                    description: "Deliver tailored customer experiences that increase engagement and loyalty."
                  }
                ].map((advantage, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                      <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="headline-large text-primary mb-2">{advantage.title}</h3>
                      <p className="body-regular text-secondary">{advantage.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/30 rounded-2xl flex items-center justify-center">
                <div className="text-center">
                  <div className="w-32 h-32 mx-auto mb-6 bg-purple-600 rounded-full flex items-center justify-center">
                    <svg className="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className="headline-large text-primary mb-2">AI-Powered Innovation</h3>
                  <p className="body-regular text-secondary">Transforming ideas into intelligent solutions</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Get Started Section */}
      <section className="py-24 bg-gradient-to-br from-purple-600 via-black to-purple-800 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                             radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                             radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)`
          }} />
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="display-medium mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="headline-medium mb-10 opacity-90">
            Join hundreds of companies already leveraging AI to drive growth and innovation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="btn-primary bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 body-large font-semibold rounded-lg transition-all duration-200"
            >
              Get Started Today
            </a>
            <a
              href="/portfolio"
              className="btn-primary bg-transparent border-2 border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4 body-large font-semibold rounded-lg transition-all duration-200"
            >
              View Our Work
            </a>
          </div>
        </div>
      </section>
    </>
  );
}
