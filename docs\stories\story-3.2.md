### Epic 3: Specialized Service Pages & Enhanced Visuals

*   **Story 3.2: Hero Section Animations (Home, OfImpact, OfLogic)**
    *   **Status:** Not Started
    *   **Story:** As a user, I want the hero sections of the Home, OfImpact, and OfLogic pages to feature engaging GSAP-driven animations, so that I am immediately captivated by the website's dynamic nature and the advanced feel of the services.
    *   **Acceptance Criteria:**
        1.  The main headlines and subheadlines in the hero sections of all three pages animate in sequentially using GSAP (e.g., character/word reveal, fade-up).
        2.  The primary CTA buttons in each hero section animate in after the text (e.g., slight scale-up and fade-in).
        3.  All hero animations are smooth, perform well across devices, and are integrated with both light and dark themes.
    *   **Tasks:**
        *   [ ] Implement GSAP animations for headline and subheadline text reveals on Home page hero.
        *   [ ] Implement GSAP animation for CTA button entrance on Home page hero.
        *   [ ] Apply similar GSAP animations to the hero sections of OfImpact and OfLogic pages.
        *   [ ] Ensure animations are performant and theme-aware.
    *   **Dev Notes:**
    *   **Testing:**
    *   **QA Results:**