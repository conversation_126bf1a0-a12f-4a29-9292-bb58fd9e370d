### Epic 5: Functional Enhancements & Credibility

*   **Story 5.1: Static Contact Information Display**
    *   **Status:** Not Started
    *   **Story:** As a user, I want to easily find `ofstartup`'s static contact information on a dedicated page and/or footer, so that I can reach out directly using various methods.
    *   **Acceptance Criteria:**
        1.  A dedicated Contact page (`app/contact/page.tsx`) is created.
        2.  A centralized configuration file (e.g., `lib/constants.ts`) is created to store all static contact information (WhatsApp number, call number, email, company name, physical address).
        3.  The Contact page displays this information clearly, with clickable links for phone numbers, email addresses, and WhatsApp.
        4.  The contact information is also displayed in the website footer.
        5.  Both displays adhere to the established design aesthetic and responsiveness.
    *   **Tasks:**
        *   [ ] Create `app/contact/page.tsx`.
        *   [ ] Create `lib/constants.ts` and define the `AppConfig` interface and data.
        *   [ ] Display contact information on the Contact page, making numbers and emails clickable.
        *   [ ] Integrate contact information into the `components/footer.tsx`.
        *   [ ] Ensure styling and responsiveness for contact displays.
    *   **Dev Notes:**
    *   **Testing:**
    *   **QA Results:**