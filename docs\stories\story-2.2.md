### Epic 2: Core Content Pages & Basic Navigation

*   **Story 2.2: Global Navigation and Footer Implementation**
    *   **Status:** Not Started
    *   **Story:** As a user, I want to easily navigate the website via a sticky navigation bar and find essential company information in a persistent footer, so that I can efficiently access different sections and resources.
    *   **Acceptance Criteria:**
        1.  A sticky navigation bar component is created and integrated into the main layout, remaining visible on scroll.
        2.  The navigation bar includes functional links to Home, About, Services, OfImpact, OfLogic, Contact, Portfolio, and Founder Deck pages.
        3.  The navigation bar is responsive, including a hamburger menu for smaller screens, and supports both light and dark themes.
        4.  A footer component is created and integrated into the main layout, present on all pages.
        5.  The footer includes copyright information and placeholder sections for additional links, adhering to the established theme.
    *   **Tasks:**
        *   [x] Create `components/navbar.tsx` for the sticky navigation bar.
        *   [x] Integrate `navbar.tsx` into `app/layout.tsx`.
        *   [x] Add navigation links to Home, About, Services, OfImpact, OfLogic, Contact, Portfolio, and Founder Deck.
        *   [x] Implement responsive behavior for the navigation bar (e.g., hamburger menu for mobile).
        *   [x] Ensure navigation bar supports light/dark themes.
        *   [x] Create `components/footer.tsx` for the website footer.
        *   [x] Integrate `footer.tsx` into `app/layout.tsx`.
        *   [x] Add copyright information and placeholder links to the footer.
        *   [x] Ensure footer supports light/dark themes.
    *   **Dev Notes:**
    *   **Testing:**
    *   **QA Results:**