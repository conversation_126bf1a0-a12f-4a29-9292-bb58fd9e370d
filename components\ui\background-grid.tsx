'use client';

import React, { useEffect, useRef } from 'react';

interface BackgroundGridProps {
  className?: string;
  enhanced?: boolean;
  animated?: boolean;
}

export const BackgroundGrid: React.FC<BackgroundGridProps> = ({ 
  className = '',
  enhanced = false,
  animated = false
}) => {
  const gridRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!animated || !gridRef.current) return;

    const grid = gridRef.current;
    let animationId: number;

    const animateGrid = () => {
      const scrollY = window.scrollY;
      const opacity = Math.min(0.02 + (scrollY / 1000) * 0.02, 0.06);
      
      grid.style.opacity = opacity.toString();
      animationId = requestAnimationFrame(animateGrid);
    };

    animateGrid();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [animated]);

  return (
    <div
      ref={gridRef}
      className={`
        bg-grid
        ${enhanced ? 'hero-active' : ''}
        ${className}
      `}
      style={{
        opacity: enhanced ? 1 : 0.5
      }}
    />
  );
};

interface NeuralNetworkProps {
  className?: string;
  nodeCount?: number;
  animated?: boolean;
}

export const NeuralNetwork: React.FC<NeuralNetworkProps> = ({
  className = '',
  nodeCount = 50,
  animated = true
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !animated) return;

    const svg = svgRef.current;
    const nodes: Array<{ x: number; y: number; element: SVGCircleElement }> = [];
    const connections: SVGLineElement[] = [];

    // Clear existing content
    svg.innerHTML = '';

    // Create nodes
    for (let i = 0; i < nodeCount; i++) {
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      
      circle.setAttribute('cx', `${x}%`);
      circle.setAttribute('cy', `${y}%`);
      circle.setAttribute('r', '2');
      circle.setAttribute('class', 'neural-node');
      circle.setAttribute('opacity', '0.6');
      
      svg.appendChild(circle);
      nodes.push({ x, y, element: circle });
    }

    // Create connections between nearby nodes
    for (let i = 0; i < nodes.length; i++) {
      for (let j = i + 1; j < nodes.length; j++) {
        const distance = Math.sqrt(
          Math.pow(nodes[i].x - nodes[j].x, 2) + 
          Math.pow(nodes[i].y - nodes[j].y, 2)
        );
        
        if (distance < 20) { // Only connect nearby nodes
          const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
          line.setAttribute('x1', `${nodes[i].x}%`);
          line.setAttribute('y1', `${nodes[i].y}%`);
          line.setAttribute('x2', `${nodes[j].x}%`);
          line.setAttribute('y2', `${nodes[j].y}%`);
          line.setAttribute('class', 'neural-path');
          line.setAttribute('opacity', '0.2');
          
          svg.appendChild(line);
          connections.push(line);
        }
      }
    }

    // Animate nodes
    const animateNodes = () => {
      nodes.forEach((node, index) => {
        const delay = index * 100;
        const duration = 2000 + Math.random() * 1000;
        
        setTimeout(() => {
          node.element.style.animation = `neuralPulse ${duration}ms ease-in-out infinite`;
        }, delay);
      });
    };

    // Animate connections
    const animateConnections = () => {
      connections.forEach((connection, index) => {
        const delay = index * 50;
        const duration = 3000 + Math.random() * 2000;
        
        setTimeout(() => {
          connection.style.animation = `neuralFlow ${duration}ms linear infinite`;
        }, delay);
      });
    };

    animateNodes();
    animateConnections();

  }, [nodeCount, animated]);

  return (
    <>
      <svg
        ref={svgRef}
        className={`
          absolute inset-0 w-full h-full pointer-events-none
          ${className}
        `}
        style={{ zIndex: -1 }}
        viewBox="0 0 100 100"
        preserveAspectRatio="xMidYMid slice"
      />
      
      {/* Add neural animation styles */}
      <style jsx>{`
        @keyframes neuralPulse {
          0%, 100% {
            opacity: 0.6;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.2);
          }
        }
        
        @keyframes neuralFlow {
          0% {
            stroke-dashoffset: 10;
            opacity: 0.2;
          }
          50% {
            opacity: 0.6;
          }
          100% {
            stroke-dashoffset: 0;
            opacity: 0.2;
          }
        }
      `}</style>
    </>
  );
};

interface FloatingParticlesProps {
  className?: string;
  particleCount?: number;
}

export const FloatingParticles: React.FC<FloatingParticlesProps> = ({
  className = '',
  particleCount = 20
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    
    // Clear existing particles
    container.innerHTML = '';

    // Create particles
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-1 h-1 bg-purple-500 rounded-full opacity-30';
      
      // Random position
      particle.style.left = `${Math.random() * 100}%`;
      particle.style.top = `${Math.random() * 100}%`;
      
      // Random animation duration and delay
      const duration = 10000 + Math.random() * 20000;
      const delay = Math.random() * 5000;
      
      particle.style.animation = `floatParticle ${duration}ms linear infinite`;
      particle.style.animationDelay = `${delay}ms`;
      
      container.appendChild(particle);
    }
  }, [particleCount]);

  return (
    <>
      <div
        ref={containerRef}
        className={`
          absolute inset-0 w-full h-full pointer-events-none overflow-hidden
          ${className}
        `}
        style={{ zIndex: -1 }}
      />
      
      {/* Particle animation styles */}
      <style jsx>{`
        @keyframes floatParticle {
          0% {
            transform: translateY(100vh) translateX(0px);
            opacity: 0;
          }
          10% {
            opacity: 0.3;
          }
          90% {
            opacity: 0.3;
          }
          100% {
            transform: translateY(-100px) translateX(${Math.random() * 200 - 100}px);
            opacity: 0;
          }
        }
      `}</style>
    </>
  );
};

// Advanced Background Pattern Component
interface AdvancedPatternProps {
  className?: string;
  pattern?: 'dots' | 'lines' | 'grid' | 'neural' | 'waves';
  intensity?: 'low' | 'medium' | 'high';
  animated?: boolean;
}

export const AdvancedPattern: React.FC<AdvancedPatternProps> = ({
  className = '',
  pattern = 'grid',
  intensity = 'medium',
  animated = true
}) => {
  const patternRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!animated || !patternRef.current) return;

    const element = patternRef.current;
    let animationId: number;

    const animatePattern = () => {
      const time = Date.now() * 0.001;
      const opacity = 0.02 + Math.sin(time * 0.5) * 0.01;

      element.style.opacity = opacity.toString();
      animationId = requestAnimationFrame(animatePattern);
    };

    animatePattern();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [animated]);

  const getPatternStyle = () => {
    const intensityMap = {
      low: 0.02,
      medium: 0.04,
      high: 0.06
    };

    const baseOpacity = intensityMap[intensity];

    switch (pattern) {
      case 'dots':
        return {
          backgroundImage: `radial-gradient(circle, rgba(138, 43, 226, ${baseOpacity}) 1px, transparent 1px)`,
          backgroundSize: '20px 20px'
        };
      case 'lines':
        return {
          backgroundImage: `linear-gradient(90deg, rgba(138, 43, 226, ${baseOpacity}) 1px, transparent 1px)`,
          backgroundSize: '20px 20px'
        };
      case 'grid':
        return {
          backgroundImage: `
            linear-gradient(rgba(138, 43, 226, ${baseOpacity}) 1px, transparent 1px),
            linear-gradient(90deg, rgba(138, 43, 226, ${baseOpacity}) 1px, transparent 1px)
          `,
          backgroundSize: '24px 24px'
        };
      case 'neural':
        return {
          backgroundImage: `
            radial-gradient(circle at 20% 50%, rgba(138, 43, 226, ${baseOpacity * 2}) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(138, 43, 226, ${baseOpacity * 1.5}) 0%, transparent 50%),
            radial-gradient(circle at 40% 80%, rgba(138, 43, 226, ${baseOpacity}) 0%, transparent 50%)
          `
        };
      case 'waves':
        return {
          backgroundImage: `repeating-linear-gradient(
            45deg,
            transparent,
            transparent 10px,
            rgba(138, 43, 226, ${baseOpacity}) 10px,
            rgba(138, 43, 226, ${baseOpacity}) 11px
          )`
        };
      default:
        return {};
    }
  };

  return (
    <div
      ref={patternRef}
      className={`
        fixed inset-0 pointer-events-none z-0
        ${className}
      `}
      style={getPatternStyle()}
    />
  );
};

// Gradient Overlay Component
interface GradientOverlayProps {
  className?: string;
  direction?: 'top' | 'bottom' | 'left' | 'right' | 'radial';
  intensity?: 'low' | 'medium' | 'high';
  color?: string;
}

export const GradientOverlay: React.FC<GradientOverlayProps> = ({
  className = '',
  direction = 'radial',
  intensity = 'medium',
  color = 'purple'
}) => {
  const getGradientStyle = () => {
    const intensityMap = {
      low: 0.05,
      medium: 0.1,
      high: 0.2
    };

    const alpha = intensityMap[intensity];
    const colorMap: Record<string, string> = {
      purple: `138, 43, 226`,
      blue: `59, 130, 246`,
      green: `16, 185, 129`,
      red: `239, 68, 68`
    };

    const rgb = colorMap[color] || colorMap.purple;

    switch (direction) {
      case 'top':
        return {
          background: `linear-gradient(to bottom, rgba(${rgb}, ${alpha}) 0%, transparent 50%)`
        };
      case 'bottom':
        return {
          background: `linear-gradient(to top, rgba(${rgb}, ${alpha}) 0%, transparent 50%)`
        };
      case 'left':
        return {
          background: `linear-gradient(to right, rgba(${rgb}, ${alpha}) 0%, transparent 50%)`
        };
      case 'right':
        return {
          background: `linear-gradient(to left, rgba(${rgb}, ${alpha}) 0%, transparent 50%)`
        };
      case 'radial':
      default:
        return {
          background: `radial-gradient(ellipse at center, rgba(${rgb}, ${alpha}) 0%, transparent 70%)`
        };
    }
  };

  return (
    <div
      className={`
        absolute inset-0 pointer-events-none
        ${className}
      `}
      style={getGradientStyle()}
    />
  );
};

export default BackgroundGrid;
