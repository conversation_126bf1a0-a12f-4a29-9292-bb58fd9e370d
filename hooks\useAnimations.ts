'use client';

import { useEffect, useRef } from 'react';
import { AnimationUtils } from '../lib/animations';

export const useTextReveal = (
  text: string,
  options: {
    type?: 'chars' | 'words';
    delay?: number;
    duration?: number;
    stagger?: number;
    ease?: string;
  } = {}
) => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    if (ref.current && text) {
      const { type = 'chars', ...animationOptions } = options;
      
      if (type === 'chars') {
        AnimationUtils.revealText(ref.current as any, animationOptions);
      } else {
        AnimationUtils.revealWords(ref.current as any, animationOptions);
      }
    }
  }, [text, options]);

  return ref;
};

export const useScrollReveal = (
  options: {
    trigger?: string;
    start?: string;
    end?: string;
    toggleActions?: string;
    y?: number;
    opacity?: number;
    duration?: number;
    stagger?: number;
    ease?: string;
    delay?: number;
  } = {}
) => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    if (ref.current) {
      AnimationUtils.scrollReveal(ref.current as any, {
        trigger: ref.current as any,
        ...options
      });
    }

    return () => {
      AnimationUtils.cleanup();
    };
  }, [options]);

  return ref;
};

export const useCardHover = () => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    if (ref.current) {
      AnimationUtils.setupCardHover(ref.current as any);
    }
  }, []);

  return ref;
};

export const useCounter = (
  endValue: number,
  options: {
    duration?: number;
    ease?: string;
    suffix?: string;
    trigger?: boolean;
  } = {}
) => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    if (ref.current && (options.trigger !== false)) {
      AnimationUtils.animateCounter(ref.current as any, endValue, options);
    }
  }, [endValue, options]);

  return ref;
};

export const useNeuralNetwork = () => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    if (ref.current) {
      AnimationUtils.animateNeuralNetwork(ref.current as any);
    }
  }, []);

  return ref;
};

export const usePageTransition = () => {
  const transition = () => {
    return AnimationUtils.pageTransition();
  };

  return { transition };
};

// Custom hook for intersection observer with animation trigger
export const useIntersectionAnimation = (
  callback: () => void,
  options: IntersectionObserverInit = {}
) => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          callback();
          observer.unobserve(element);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px',
        ...options
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [callback, options]);

  return ref;
};

// Hook for managing multiple animations on a single element
export const useMultipleAnimations = () => {
  const ref = useRef<HTMLElement>(null);

  const revealText = (options = {}) => {
    if (ref.current) {
      AnimationUtils.revealText(ref.current as any, options);
    }
  };

  const scrollReveal = (options = {}) => {
    if (ref.current) {
      AnimationUtils.scrollReveal(ref.current as any, {
        trigger: ref.current as any,
        ...options
      });
    }
  };

  const setupHover = () => {
    if (ref.current) {
      AnimationUtils.setupCardHover(ref.current as any);
    }
  };

  return {
    ref,
    revealText,
    scrollReveal,
    setupHover
  };
};

export default {
  useTextReveal,
  useScrollReveal,
  useCardHover,
  useCounter,
  useNeuralNetwork,
  usePageTransition,
  useIntersectionAnimation,
  useMultipleAnimations
};
