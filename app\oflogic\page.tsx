import React from 'react';
import { ShoppingCart, Tv, Palette, Tag, DollarSign, FileText } from 'lucide-react';
import { StatsCard, FeatureCard } from '../../components/ui/custom-card';

export const metadata = {
  title: 'OfLogic - Neuromarketing & Consumer Behavior Analysis | ofstartup.ai',
  description: 'Unlock the power of consumer psychology with OfLogic, our advanced AI-powered neuromarketing platform. Analyze consumer behavior, optimize marketing campaigns, and boost conversions with cutting-edge neuroscience.',
  keywords: [
    'OfLogic',
    'neuromarketing',
    'consumer behavior analysis',
    'AI marketing',
    'consumer psychology',
    'marketing optimization',
    'behavioral analytics',
    'neuroscience marketing',
    'conversion optimization',
    'marketing intelligence'
  ],
  openGraph: {
    title: 'OfLogic - AI-Powered Neuromarketing Platform | ofstartup.ai',
    description: 'Unlock consumer psychology with OfLogic. Advanced neuromarketing platform for behavior analysis and marketing optimization.',
    type: 'website',
    locale: 'en_US',
    url: 'https://ofstartupglobal.com/oflogic',
    siteName: 'ofstartup.ai',
    images: [
      {
        url: '/api/og?title=OfLogic%20-%20AI-Powered%20Neuromarketing&description=Unlock%20consumer%20psychology%20with%20advanced%20neuromarketing%20platform&page=oflogic',
        width: 1200,
        height: 630,
        alt: 'OfLogic - Neuromarketing Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'OfLogic - AI-Powered Neuromarketing Platform | ofstartup.ai',
    description: 'Unlock consumer psychology with OfLogic. Advanced neuromarketing platform for behavior analysis.',
    images: ['/api/og?title=OfLogic%20-%20AI-Powered%20Neuromarketing&description=Unlock%20consumer%20psychology%20with%20advanced%20neuromarketing%20platform&page=oflogic'],
  },
  alternates: {
    canonical: 'https://ofstartupglobal.com/oflogic',
  },
};

export default function OfLogicPage() {
  const neuroFeatures = [
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
      title: "Brain Activity Analysis",
      description: "Advanced EEG and neuroimaging analysis to understand consumer cognitive responses"
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      ),
      title: "Eye Tracking Insights",
      description: "Precise eye movement tracking to optimize visual design and user attention"
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
      title: "Emotional Response",
      description: "Measure emotional engagement and sentiment to create compelling experiences"
    }
  ];

  return (
    <>
      {/* What is OfLogic Section */}
      <section className="py-36 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col gap-16 items-center">
            <div className='flex flex-col items-center justify-center text-center'>
              <h2 className="display-medium text-primary mb-6">
                The Science of Consumer Behavior
              </h2>
              <p className="headline-medium text-secondary mb-8">
                OfLogic combines neuroscience, psychology, and artificial intelligence to decode how consumers think, feel, and make purchasing decisions.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {neuroFeatures.map((feature, index) => (
                <FeatureCard
                  key={index}
                  icon={feature.icon}
                  title={feature.title}
                  description={feature.description}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Neuromarketing Metrics */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Measurable Neuromarketing Impact
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              See how OfLogic&apos;s neuromarketing insights translate into real business results.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <StatsCard
              title="Conversion Rate"
              value="67%"
              description="Average increase in conversions"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              }
              trend={{ value: 32, isPositive: true }}
            />
            <StatsCard
              title="Engagement Time"
              value="3.2x"
              description="Longer user engagement periods"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              trend={{ value: 45, isPositive: true }}
            />
            <StatsCard
              title="Prediction Accuracy"
              value="91%"
              description="Consumer behavior prediction rate"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              trend={{ value: 18, isPositive: true }}
            />
            <StatsCard
              title="Campaign ROI"
              value="340%"
              description="Return on marketing investment"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              }
              trend={{ value: 55, isPositive: true }}
            />
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-24 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Neuromarketing Applications
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Discover how different industries leverage OfLogic to understand and influence consumer behavior.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "E-commerce Optimization",
                description: "Optimize product pages, checkout flows, and user journeys based on neurological responses.",
                icon: <ShoppingCart className="w-8 h-8" />,
                metrics: "45% increase in conversions"
              },
              {
                title: "Advertisement Testing",
                description: "Test ad effectiveness before launch using brain activity and emotional response data.",
                icon: <Tv className="w-8 h-8" />,
                metrics: "60% better ad performance"
              },
              {
                title: "Product Design",
                description: "Design products that trigger positive emotional responses and purchase intent.",
                icon: <Palette className="w-8 h-8" />,
                metrics: "35% higher user satisfaction"
              },
              {
                title: "Brand Positioning",
                description: "Understand how consumers perceive your brand at a subconscious level.",
                icon: <Tag className="w-8 h-8" />,
                metrics: "50% stronger brand recall"
              },
              {
                title: "Pricing Strategy",
                description: "Determine optimal pricing based on psychological and neurological responses.",
                icon: <DollarSign className="w-8 h-8" />,
                metrics: "25% revenue increase"
              },
              {
                title: "Content Marketing",
                description: "Create content that resonates emotionally and drives engagement.",
                icon: <FileText className="w-8 h-8" />,
                metrics: "80% higher engagement"
              }
            ].map((useCase, index) => (
              <div key={index} className="card p-6 text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center text-purple-600 dark:text-purple-400 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20">{useCase.icon}</div>
                <h3 className="headline-large text-primary mb-3">{useCase.title}</h3>
                <p className="body-regular text-secondary mb-4">{useCase.description}</p>
                <div className="text-sm font-semibold text-purple-600 dark:text-purple-400">
                  {useCase.metrics}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-purple-600 via-black to-purple-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="display-medium mb-6">
            Unlock Consumer Psychology
          </h2>
          <p className="headline-medium mb-10 opacity-90">
            Start understanding your customers at a deeper level with OfLogic&apos;s neuromarketing platform.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="btn-primary bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 body-large font-semibold rounded-lg"
            >
              Request Demo
            </a>
            <a
              href="/portfolio"
              className="btn-primary bg-transparent border-2 border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4 body-large font-semibold rounded-lg"
            >
              View Research
            </a>
          </div>
        </div>
      </section>
    </>
  );
}
