import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import SplitType from 'split-type';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// GSAP Configuration for optimal performance
gsap.config({
  force3D: true,
  nullTargetWarn: false
});

// Animation Utilities
export class AnimationUtils {
  
  /**
   * Character-by-character text reveal animation
   */
  static revealText(selector: string, options: {
    delay?: number;
    duration?: number;
    stagger?: number;
    ease?: string;
    y?: number;
    opacity?: number;
  } = {}) {
    const {
      delay = 0,
      duration = 0.8,
      stagger = 0.02,
      ease = "back.out(1.7)",
      y = 100,
      opacity = 0
    } = options;

    if (typeof window === 'undefined') return;

    const elements = document.querySelectorAll(selector);
    
    elements.forEach((element) => {
      const splitText = new SplitType(element as HTMLElement, { types: 'chars' });
      
      gsap.set(splitText.chars, {
        y,
        opacity,
        rotateX: -90
      });

      gsap.to(splitText.chars, {
        y: 0,
        opacity: 1,
        rotateX: 0,
        duration,
        stagger,
        ease,
        delay
      });
    });
  }

  /**
   * Word-by-word text reveal animation
   */
  static revealWords(selector: string, options: {
    delay?: number;
    duration?: number;
    stagger?: number;
    ease?: string;
    y?: number;
  } = {}) {
    const {
      delay = 0,
      duration = 0.6,
      stagger = 0.08,
      ease = "power3.out",
      y = 50
    } = options;

    if (typeof window === 'undefined') return;

    const elements = document.querySelectorAll(selector);
    
    elements.forEach((element) => {
      const splitText = new SplitType(element as HTMLElement, { types: 'words' });
      
      gsap.from(splitText.words, {
        y,
        opacity: 0,
        duration,
        stagger,
        ease,
        delay
      });
    });
  }

  /**
   * Scroll-triggered animations
   */
  static scrollReveal(selector: string, options: {
    trigger?: string;
    start?: string;
    end?: string;
    toggleActions?: string;
    y?: number;
    opacity?: number;
    duration?: number;
    stagger?: number;
    ease?: string;
  } = {}) {
    const {
      trigger,
      start = 'top 85%',
      end = 'bottom 20%',
      toggleActions = 'play none none reverse',
      y = 80,
      opacity = 0,
      duration = 0.8,
      stagger = 0.15,
      ease = "power2.out"
    } = options;

    if (typeof window === 'undefined') return;

    gsap.from(selector, {
      scrollTrigger: {
        trigger: trigger || selector,
        start,
        end,
        toggleActions
      },
      y,
      opacity,
      duration,
      stagger,
      ease
    });
  }

  /**
   * Card hover animations
   */
  static setupCardHover(selector: string) {
    if (typeof window === 'undefined') return;

    const cards = document.querySelectorAll(selector);
    
    cards.forEach((card) => {
      const icon = card.querySelector('.card-icon');
      const content = card.querySelector('.card-content');
      
      const hoverTl = gsap.timeline({ paused: true });
      
      hoverTl
        .to(card, {
          y: -8,
          boxShadow: '0 20px 60px rgba(138, 43, 226, 0.15)',
          duration: 0.3,
          ease: "power2.out"
        })
        .to(icon, {
          scale: 1.1,
          rotation: 5,
          duration: 0.2,
          ease: "back.out(2)"
        }, "<")
        .to(content, {
          y: -4,
          duration: 0.3,
          ease: "power2.out"
        }, "<");
        
      card.addEventListener('mouseenter', () => hoverTl.play());
      card.addEventListener('mouseleave', () => hoverTl.reverse());
    });
  }

  /**
   * Background grid intensity animation
   */
  static animateBackgroundGrid() {
    if (typeof window === 'undefined') return;

    ScrollTrigger.create({
      trigger: '.hero-section',
      start: 'top center',
      end: 'bottom center',
      onUpdate: (self) => {
        const progress = self.progress;
        gsap.to('.bg-grid', {
          opacity: 0.02 + (progress * 0.02),
          duration: 0.3
        });
      }
    });
  }

  /**
   * Neural network animation
   */
  static animateNeuralNetwork(container: string) {
    if (typeof window === 'undefined') return;

    // Animate node pulsing
    gsap.to(`${container} .neural-node`, {
      scale: 1.2,
      opacity: 0.8,
      duration: 2,
      stagger: {
        each: 0.1,
        repeat: -1,
        yoyo: true
      },
      ease: "sine.inOut"
    });

    // Animate connection paths
    gsap.fromTo(`${container} .neural-path`, 
      { strokeDashoffset: 100 },
      {
        strokeDashoffset: 0,
        duration: 3,
        stagger: 0.2,
        repeat: -1,
        ease: "none"
      }
    );
  }

  /**
   * Page transition animations
   */
  static pageTransition() {
    if (typeof window === 'undefined') return;

    const tl = gsap.timeline();
    
    tl.to('.page-transition', {
      scaleY: 1,
      duration: 0.5,
      ease: "power2.inOut",
      transformOrigin: "bottom"
    })
    .to('.page-transition', {
      scaleY: 0,
      duration: 0.5,
      ease: "power2.inOut",
      transformOrigin: "top",
      delay: 0.2
    });

    return tl;
  }

  /**
   * Counter animation for stats
   */
  static animateCounter(selector: string, endValue: number, options: {
    duration?: number;
    ease?: string;
    suffix?: string;
  } = {}) {
    const {
      duration = 2,
      ease = "power2.out",
      suffix = ""
    } = options;

    if (typeof window === 'undefined') return;

    const element = document.querySelector(selector);
    if (!element) return;

    const obj = { value: 0 };
    
    gsap.to(obj, {
      value: endValue,
      duration,
      ease,
      onUpdate: () => {
        element.textContent = Math.round(obj.value) + suffix;
      }
    });
  }

  /**
   * Cleanup function for ScrollTrigger
   */
  static cleanup() {
    if (typeof window !== 'undefined') {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    }
  }
}

// Framer Motion variants for common animations
export const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const fadeIn = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  transition: { duration: 0.6 }
};

export const scaleIn = {
  initial: { opacity: 0, scale: 0.9 },
  animate: { opacity: 1, scale: 1 },
  transition: { duration: 0.5, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const slideInLeft = {
  initial: { opacity: 0, x: -60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const slideInRight = {
  initial: { opacity: 0, x: 60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] }
};

export const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

export const staggerItem = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 }
};

export default AnimationUtils;
