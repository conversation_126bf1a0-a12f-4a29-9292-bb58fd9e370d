import Link from 'next/link';
import React from 'react';

export default function PrivacyPage() {
  return (
    <main className="min-h-screen pt-24 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h1 className="display-large text-primary mb-6">Privacy Policy</h1>
          <p className="headline-medium text-secondary">
            Your privacy is important to us. This policy outlines how we collect, use, and protect your information.
          </p>
        </div>

        <div className="prose prose-lg dark:prose-invert max-w-none">
          <div className="bg-white/50 dark:bg-black/50 backdrop-blur-sm rounded-xl p-8 border border-gray-200/50 dark:border-white/10">
            <h2 className="text-2xl font-bold text-primary mb-4">Information We Collect</h2>
            <p className="text-secondary mb-6">
              We collect information you provide directly to us, such as when you contact us for services, 
              subscribe to our newsletter, or interact with our AI solutions.
            </p>

            <h2 className="text-2xl font-bold text-primary mb-4">How We Use Your Information</h2>
            <p className="text-secondary mb-6">
              We use the information we collect to provide, maintain, and improve our AI services, 
              communicate with you, and ensure the security of our platform.
            </p>

            <h2 className="text-2xl font-bold text-primary mb-4">Data Protection</h2>
            <p className="text-secondary mb-6">
              We implement appropriate security measures to protect your personal information against 
              unauthorized access, alteration, disclosure, or destruction.
            </p>

            <h2 className="text-2xl font-bold text-primary mb-4">Contact Us</h2>
            <p className="text-secondary">
              If you have any questions about this Privacy Policy, please contact us {` `}
              <Link href={`/contact`} className='text-purple-500 dark:text-purple-400 hover:underline'>here</Link>
            </p>
          </div>
        </div>
      </div>
    </main>
  );
}
