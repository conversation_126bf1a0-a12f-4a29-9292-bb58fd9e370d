import <PERSON> from 'papaparse';

export interface PortfolioProject {
  project_id: string;
  title: string;
  description: string;
  domain: string;
  start_date: string;
  end_date: string;
  delivery_time_days: number;
  technologies: string;
  complexity_level: 'Low' | 'Medium' | 'High';
  year: number;
  project_url: string;
}

export interface PaginatedPortfolioData {
  projects: PortfolioProject[];
  totalProjects: number;
  totalPages: number;
  currentPage: number;
  projectsPerPage: number;
}

export interface PortfolioFilters {
  year?: number[];
  domain?: string[];
  complexity?: ('Low' | 'Medium' | 'High')[];
  technologies?: string[];
  searchQuery?: string;
}

export class PortfolioParser {
  private static portfolioData: PortfolioProject[] | null = null;

  /**
   * Load and parse portfolio CSV data
   */
  static async loadPortfolioData(): Promise<PortfolioProject[]> {
    if (this.portfolioData) {
      return this.portfolioData;
    }

    try {
      const response = await fetch('/data/portfolio.csv');
      if (!response.ok) {
        throw new Error(`Failed to fetch portfolio data: ${response.statusText}`);
      }
      
      const csvData = await response.text();
      
      const parsed = Papa.parse<PortfolioProject>(csvData, {
        header: true,
        skipEmptyLines: true,
        transform: (value: string, field: string) => {
          // Transform specific fields to appropriate types
          switch (field) {
            case 'delivery_time_days':
            case 'year':
              return parseInt(value, 10);
            case 'project_id':
              return value;
            default:
              return value.trim();
          }
        }
      });

      if (parsed.errors.length > 0) {
        console.warn('CSV parsing errors:', parsed.errors);
      }

      this.portfolioData = parsed.data.filter(project => 
        project.title && project.description && project.domain
      );

      return this.portfolioData;
    } catch (error) {
      console.error('Error loading portfolio data:', error);
      throw new Error('Failed to load portfolio data');
    }
  }

  /**
   * Get paginated portfolio data with optional filters
   */
  static async getPaginatedData(
    page: number = 1,
    projectsPerPage: number = 12,
    filters?: PortfolioFilters
  ): Promise<PaginatedPortfolioData> {
    const allProjects = await this.loadPortfolioData();
    let filteredProjects = [...allProjects];

    // Apply filters
    if (filters) {
      filteredProjects = this.applyFilters(filteredProjects, filters);
    }

    // Sort by end_date (most recent completed projects first)
    filteredProjects.sort((a, b) => {
      const dateA = new Date(a.end_date);
      const dateB = new Date(b.end_date);
      return dateB.getTime() - dateA.getTime();
    });

    const totalProjects = filteredProjects.length;
    const totalPages = Math.ceil(totalProjects / projectsPerPage);
    const startIndex = (page - 1) * projectsPerPage;
    const endIndex = startIndex + projectsPerPage;
    const projects = filteredProjects.slice(startIndex, endIndex);

    return {
      projects,
      totalProjects,
      totalPages,
      currentPage: page,
      projectsPerPage
    };
  }

  /**
   * Apply filters to portfolio projects
   */
  private static applyFilters(
    projects: PortfolioProject[],
    filters: PortfolioFilters
  ): PortfolioProject[] {
    return projects.filter(project => {
      // Year filter
      if (filters.year && filters.year.length > 0) {
        if (!filters.year.includes(project.year)) {
          return false;
        }
      }

      // Domain filter
      if (filters.domain && filters.domain.length > 0) {
        if (!filters.domain.includes(project.domain)) {
          return false;
        }
      }

      // Complexity filter
      if (filters.complexity && filters.complexity.length > 0) {
        if (!filters.complexity.includes(project.complexity_level)) {
          return false;
        }
      }

      // Technologies filter (partial match)
      if (filters.technologies && filters.technologies.length > 0) {
        const projectTech = project.technologies.toLowerCase();
        const hasMatchingTech = filters.technologies.some(tech =>
          projectTech.includes(tech.toLowerCase())
        );
        if (!hasMatchingTech) {
          return false;
        }
      }

      // Search query (searches title, description, domain, technologies)
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        const searchableText = [
          project.title,
          project.description,
          project.domain,
          project.technologies
        ].join(' ').toLowerCase();
        
        if (!searchableText.includes(query)) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Get unique values for filter options
   */
  static async getFilterOptions(): Promise<{
    years: number[];
    domains: string[];
    technologies: string[];
    complexityLevels: ('Low' | 'Medium' | 'High')[];
  }> {
    const allProjects = await this.loadPortfolioData();

    const years = Array.from(new Set(allProjects.map(p => p.year))).sort((a, b) => b - a);
    const domains = Array.from(new Set(allProjects.map(p => p.domain))).sort();
    
    // Extract individual technologies from comma-separated strings
    const allTechnologies = allProjects.flatMap(p => 
      p.technologies.split(',').map(tech => tech.trim())
    );
    const technologies = Array.from(new Set(allTechnologies)).sort();
    
    const complexityLevels: ('Low' | 'Medium' | 'High')[] = ['Low', 'Medium', 'High'];

    return {
      years,
      domains,
      technologies,
      complexityLevels
    };
  }

  /**
   * Get project statistics
   */
  static async getProjectStats(): Promise<{
    totalProjects: number;
    avgDeliveryTime: number;
    avgEfficiencyGains: number;
    projectsByYear: { year: number; count: number }[];
    projectsByComplexity: { complexity: string; count: number }[];
    topDomains: { domain: string; count: number }[];
    topTechnologies: { technology: string; count: number }[];
  }> {
    const allProjects = await this.loadPortfolioData();

    const totalProjects = allProjects.length;
    
    const avgDeliveryTime = Math.round(
      allProjects.reduce((sum, p) => sum + p.delivery_time_days, 0) / totalProjects
    );

    // Calculate average efficiency improvement over time (based on year progression)
    const avgEfficiencyGains = Math.round(
      allProjects.reduce((sum, p) => {
        // Simulate efficiency gains based on year progression and use of modern tools
        const baseEfficiency = (p.year - 2019) * 5; // 5% per year base improvement
        const complexityBonus = p.complexity_level === 'High' ? 10 : p.complexity_level === 'Medium' ? 5 : 0;
        return sum + Math.min(baseEfficiency + complexityBonus, 40); // Cap at 40%
      }, 0) / totalProjects
    );

    // Projects by year
    const yearCounts = allProjects.reduce((acc, p) => {
      acc[p.year] = (acc[p.year] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);
    const projectsByYear = Object.entries(yearCounts)
      .map(([year, count]) => ({ year: parseInt(year, 10), count }))
      .sort((a, b) => a.year - b.year);

    // Projects by complexity
    const complexityCounts = allProjects.reduce((acc, p) => {
      acc[p.complexity_level] = (acc[p.complexity_level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    const projectsByComplexity = Object.entries(complexityCounts)
      .map(([complexity, count]) => ({ complexity, count }))
      .sort((a, b) => b.count - a.count);

    // Top domains
    const domainCounts = allProjects.reduce((acc, p) => {
      acc[p.domain] = (acc[p.domain] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    const topDomains = Object.entries(domainCounts)
      .map(([domain, count]) => ({ domain, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Top technologies
    const techCounts = allProjects.flatMap(p =>
      p.technologies.split(',').map(tech => tech.trim())
    ).reduce((acc, tech) => {
      acc[tech] = (acc[tech] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    const topTechnologies = Object.entries(techCounts)
      .map(([technology, count]) => ({ technology, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 15);

    return {
      totalProjects,
      avgDeliveryTime,
      avgEfficiencyGains,
      projectsByYear,
      projectsByComplexity,
      topDomains,
      topTechnologies
    };
  }

  /**
   * Get a specific project by ID
   */
  static async getProjectById(projectId: string): Promise<PortfolioProject | null> {
    const allProjects = await this.loadPortfolioData();
    return allProjects.find(p => p.project_id === projectId) || null;
  }

  /**
   * Search projects by query
   */
  static async searchProjects(
    query: string,
    limit: number = 10
  ): Promise<PortfolioProject[]> {
    const allProjects = await this.loadPortfolioData();
    const filteredProjects = this.applyFilters(allProjects, { searchQuery: query });
    return filteredProjects.slice(0, limit);
  }
}

export default PortfolioParser;