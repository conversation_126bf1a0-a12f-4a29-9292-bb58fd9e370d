@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* ============================================================================
   OFSTARTUP.AI VISUAL DESIGN SYSTEM
   Scale.ai-Quality Premium Aesthetics
   ============================================================================ */

:root {
  /* ===== PRIMARY PURPLE COLOR SYSTEM ===== */
  --primary-purple: #8A2BE2;
  --primary-purple-80: rgba(138, 43, 226, 0.8);
  --primary-purple-60: rgba(138, 43, 226, 0.6);
  --primary-purple-40: rgba(138, 43, 226, 0.4);
  --primary-purple-20: rgba(138, 43, 226, 0.2);
  --primary-purple-10: rgba(138, 43, 226, 0.1);

  /* ===== SCALE.AI STYLE GLOW COLORS ===== */
  --glow-purple: rgba(138, 43, 226, 0.4);
  --glow-purple-soft: rgba(138, 43, 226, 0.2);
  --glow-purple-subtle: rgba(138, 43, 226, 0.1);
  --glow-blue: rgba(59, 130, 246, 0.3);
  --glow-blue-soft: rgba(59, 130, 246, 0.15);
  --border-glow: rgba(138, 43, 226, 0.3);
  --border-glow-hover: rgba(138, 43, 226, 0.5);

  /* ===== LIGHT THEME COLORS - Scale.ai Style ===== */
  --bg-primary-light: #FFFFFF;
  --bg-secondary-light: #FFFFFF;
  --bg-surface-light: rgba(255, 255, 255, 0.8);
  --border-light: rgba(0, 0, 0, 0.1);
  --border-hover-light: rgba(0, 0, 0, 0.2);

  --text-primary-light: #000000;
  --text-secondary-light: #666666;
  --text-tertiary-light: #999999;
  --text-disabled-light: #CCCCCC;

  /* ===== DARK THEME COLORS - Scale.ai Style ===== */
  --bg-primary-dark: #000000;
  --bg-secondary-dark: #000000;
  --bg-surface-dark: rgba(0, 0, 0, 0.8);
  --border-dark: rgba(255, 255, 255, 0.1);
  --border-hover-dark: rgba(255, 255, 255, 0.2);

  --text-primary-dark: #FFFFFF;
  --text-secondary-dark: #CCCCCC;
  --text-tertiary-dark: #999999;
  --text-disabled-dark: #666666;

  /* ===== VISUAL IMPACT COLORS ===== */
  --success: #30D158;
  --warning: #FF9F0A;
  --error: #FF3B30;
  --info: #007AFF;

  /* ===== GRADIENTS ===== */
  --gradient-purple: linear-gradient(135deg, #8A2BE2 0%, #4B0082 100%);
  --gradient-neural: linear-gradient(45deg, #8A2BE2 0%, #9370DB 50%, #BA55D3 100%);
  --gradient-overlay: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.4) 100%);

  /* ===== DEFAULT TO LIGHT THEME ===== */
  --bg-primary: var(--bg-primary-light);
  --bg-secondary: var(--bg-secondary-light);
  --bg-surface: var(--bg-surface-light);
  --border-color: var(--border-light);

  --text-primary: var(--text-primary-light);
  --text-secondary: var(--text-secondary-light);
  --text-tertiary: var(--text-tertiary-light);
  --text-disabled: var(--text-disabled-light);

  /* ===== GRID COLORS ===== */
  --grid-light: rgba(0, 0, 0, 0.03);
  --grid-dark: rgba(255, 255, 255, 0.02);
  --grid-color: var(--grid-light);

  /* ===== TYPOGRAPHY SCALE ===== */
  --font-display-large: 4.5rem;      /* 72px */
  --font-display-medium: 3.5rem;     /* 56px */
  --font-display-small: 2.75rem;     /* 44px */
  --font-headline-large: 2rem;       /* 32px */
  --font-headline-medium: 1.5rem;    /* 24px */
  --font-body-large: 1.125rem;       /* 18px */
  --font-body-regular: 1rem;         /* 16px */
  --font-body-small: 0.875rem;       /* 14px */
  --font-label: 0.75rem;             /* 12px */

  /* ===== FONT WEIGHTS ===== */
  --weight-light: 300;
  --weight-regular: 400;
  --weight-medium: 500;
  --weight-semibold: 600;
  --weight-bold: 700;

  /* ===== LINE HEIGHTS ===== */
  --line-height-tight: 1.1;
  --line-height-normal: 1.15;
  --line-height-relaxed: 1.2;
  --line-height-comfortable: 1.25;
  --line-height-loose: 1.3;
  --line-height-body: 1.6;
  --line-height-small: 1.5;
  --line-height-label: 1.4;

  /* ===== LETTER SPACING ===== */
  --letter-spacing-tight: -0.02em;
  --letter-spacing-normal: -0.015em;
  --letter-spacing-relaxed: -0.01em;
  --letter-spacing-wide: 0.02em;

  /* ===== SPACING SCALE ===== */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-32: 8rem;     /* 128px */

  /* ===== BORDER RADIUS ===== */
  --radius-sm: 0.375rem;   /* 6px */
  --radius-md: 0.5rem;     /* 8px */
  --radius-lg: 0.75rem;    /* 12px */
  --radius-xl: 1rem;       /* 16px */
  --radius-2xl: 1.5rem;    /* 24px */

  /* ===== SHADOWS ===== */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  --shadow-purple: 0 8px 32px rgba(138, 43, 226, 0.15);
  --shadow-purple-hover: 0 20px 60px rgba(138, 43, 226, 0.25);
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

/* ===== DARK THEME ===== */
[data-theme="dark"] {
  --bg-primary: var(--bg-primary-dark);
  --bg-secondary: var(--bg-secondary-dark);
  --bg-surface: var(--bg-surface-dark);
  --border-color: var(--border-dark);

  --text-primary: var(--text-primary-dark);
  --text-secondary: var(--text-secondary-dark);
  --text-tertiary: var(--text-tertiary-dark);
  --text-disabled: var(--text-disabled-dark);

  --grid-color: var(--grid-dark);

  /* Enhanced shadows for dark theme */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.4);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.5);
}

/* ===== SYSTEM PREFERENCE DETECTION ===== */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    --bg-primary: var(--bg-primary-dark);
    --bg-secondary: var(--bg-secondary-dark);
    --bg-surface: var(--bg-surface-dark);
    --border-color: var(--border-dark);

    --text-primary: var(--text-primary-dark);
    --text-secondary: var(--text-secondary-dark);
    --text-tertiary: var(--text-tertiary-dark);
    --text-disabled: var(--text-disabled-dark);

    --grid-color: var(--grid-dark);
  }
}

/* ===== GLOBAL THEME TRANSITIONS ===== */
* {
  transition: 
    background-color 300ms cubic-bezier(0.4, 0, 0.2, 1),
    color 300ms cubic-bezier(0.4, 0, 0.2, 1),
    border-color 300ms cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== FONT FAMILIES ===== */
:root {
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
  --font-mono: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Fira Code', monospace;
}

/* ===== BASE BODY STYLES ===== */
html {
  font-feature-settings: 'kern' 1, 'liga' 1, 'ss01' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-sans);
  font-size: var(--font-body-regular);
  font-weight: var(--weight-regular);
  line-height: var(--line-height-body);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY UTILITY CLASSES ===== */
.display-large {
  font-size: var(--font-display-large);
  font-weight: var(--weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
}

.display-medium {
  font-size: var(--font-display-medium);
  font-weight: var(--weight-semibold);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-normal);
}

.display-small {
  font-size: var(--font-display-small);
  font-weight: var(--weight-semibold);
  line-height: var(--line-height-relaxed);
  letter-spacing: var(--letter-spacing-relaxed);
}

.headline-large {
  font-size: var(--font-headline-large);
  font-weight: var(--weight-semibold);
  line-height: var(--line-height-comfortable);
}

.headline-medium {
  font-size: var(--font-headline-medium);
  font-weight: var(--weight-medium);
  line-height: var(--line-height-loose);
}

.body-large {
  font-size: var(--font-body-large);
  font-weight: var(--weight-regular);
  line-height: var(--line-height-body);
}

.body-regular {
  font-size: var(--font-body-regular);
  font-weight: var(--weight-regular);
  line-height: var(--line-height-body);
}

.body-small {
  font-size: var(--font-body-small);
  font-weight: var(--weight-regular);
  line-height: var(--line-height-small);
}

.label {
  font-size: var(--font-label);
  font-weight: var(--weight-medium);
  line-height: var(--line-height-label);
  letter-spacing: var(--letter-spacing-wide);
  text-transform: uppercase;
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */
@media (max-width: 767px) {
  .display-large {
    font-size: 3rem; /* 48px */
  }

  .display-medium {
    font-size: 2.25rem; /* 36px */
  }

  .display-small {
    font-size: 1.75rem; /* 28px */
  }

  .headline-large {
    font-size: 1.5rem; /* 24px */
  }

  .body-large {
    font-size: 1rem; /* 16px */
  }
}

/* ===== COLOR UTILITY CLASSES ===== */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-purple { color: var(--primary-purple); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-info { color: var(--info); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-surface { background-color: var(--bg-surface); }
.bg-purple { background-color: var(--primary-purple); }

/* Scale.ai style section backgrounds with subtle effects */
.section-bg-primary {
  background: var(--bg-primary);
  position: relative;
}

.section-bg-secondary {
  background: var(--bg-secondary);
  position: relative;
}

/* Add subtle gradient overlays for depth */
.section-bg-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%,
    var(--glow-purple-subtle) 0%,
    transparent 50%);
  pointer-events: none;
  opacity: 0.3;
}

.section-bg-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 80% 80%,
    var(--glow-blue-soft) 0%,
    transparent 50%);
  pointer-events: none;
  opacity: 0.2;
}

.border-default { border-color: var(--border-color); }
.border-purple { border-color: var(--primary-purple); }

/* ===== SCROLL BEHAVIOR ===== */
html {
  scroll-behavior: smooth;
}

/* ===== FOCUS STYLES ===== */
*:focus {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* ===== SELECTION STYLES ===== */
::selection {
  background-color: var(--primary-purple-20);
  color: var(--text-primary);
}

::-moz-selection {
  background-color: var(--primary-purple-20);
  color: var(--text-primary);
}

/* ===== BACKGROUND GRID SYSTEM ===== */
.bg-grid {
  background-image:
    linear-gradient(var(--grid-color) 1px, transparent 1px),
    linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
  background-size: 24px 24px;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  transition: opacity 300ms ease;
}

/* Enhanced grid for hero sections */
.hero-active .bg-grid {
  opacity: 1;
  background-image:
    linear-gradient(rgba(138, 43, 226, 0.04) 1px, transparent 1px),
    linear-gradient(90deg, rgba(138, 43, 226, 0.04) 1px, transparent 1px);
}

/* Hero section background override */
.hero-section {
  background-color: white !important;
}

.dark .hero-section {
  background-color: black !important;
}

/* ===== BUTTON SYSTEM ===== */
.btn-primary {
  background: linear-gradient(135deg,
    var(--primary-purple) 0%,
    rgba(138, 43, 226, 0.9) 100%);
  border: 1px solid var(--border-glow);
  border-radius: 8px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* Scale.ai style button glow */
  box-shadow:
    0 0 0 1px var(--glow-purple-soft),
    0 4px 12px rgba(138, 43, 226, 0.2),
    0 2px 6px rgba(138, 43, 226, 0.1);
}

/* Enhanced hover state with glow */
.btn-primary:hover {
  transform: translateY(-2px);
  border-color: var(--border-glow-hover);

  box-shadow:
    0 0 0 1px var(--border-glow-hover),
    0 0 16px var(--glow-purple),
    0 8px 24px rgba(138, 43, 226, 0.3),
    0 4px 12px rgba(138, 43, 226, 0.2);
}

.btn-primary:active {
  transform: translateY(0px) scale(0.98);
  transition-duration: 100ms;
}

/* Button shimmer effect */
.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

/* ===== CARD SYSTEM - Scale.ai Style ===== */
.card {
  /* Base card styling */
  background: linear-gradient(135deg,
    var(--bg-surface) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  /* Scale.ai style subtle glow */
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 1px 4px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Subtle inner glow effect */
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 0%,
    var(--glow-purple-subtle) 0%,
    transparent 50%);
  opacity: 0.5;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

/* Enhanced hover state with glowing border */
.card:hover {
  transform: translateY(-4px);
  border-color: var(--border-glow);

  /* Multi-layered glow effect */
  box-shadow:
    0 0 0 1px var(--border-glow),
    0 8px 32px rgba(138, 43, 226, 0.2),
    0 4px 16px rgba(138, 43, 226, 0.1),
    0 20px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.card:hover::before {
  opacity: 0.8;
}

/* Featured cards with enhanced glow */
.card-glow {
  border-color: var(--border-glow-hover);

  /* Permanent subtle glow for featured cards */
  box-shadow:
    0 0 0 1px var(--glow-purple-soft),
    0 8px 24px rgba(138, 43, 226, 0.15),
    0 4px 12px rgba(138, 43, 226, 0.1),
    0 16px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Enhanced glow on hover for featured cards */
.card-glow:hover {
  border-color: var(--border-glow-hover);

  box-shadow:
    0 0 0 1px var(--border-glow-hover),
    0 0 20px var(--glow-purple),
    0 12px 40px rgba(138, 43, 226, 0.25),
    0 6px 20px rgba(138, 43, 226, 0.15),
    0 24px 48px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

/* Subtle animation for glow effect */
@keyframes subtle-glow {
  0%, 100% {
    box-shadow:
      0 0 0 1px var(--glow-purple-soft),
      0 8px 24px rgba(138, 43, 226, 0.15);
  }
  50% {
    box-shadow:
      0 0 0 1px var(--glow-purple),
      0 8px 24px rgba(138, 43, 226, 0.25);
  }
}

.card-glow {
  animation: subtle-glow 4s ease-in-out infinite;
}

/* ===== FORM ELEMENTS - Scale.ai Style ===== */
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);

  /* Subtle glow */
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--border-glow);

  /* Enhanced glow on focus */
  box-shadow:
    0 0 0 1px var(--border-glow),
    0 0 12px var(--glow-purple-soft),
    0 4px 16px rgba(138, 43, 226, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Placeholder styling */
input::placeholder,
textarea::placeholder {
  color: var(--text-tertiary);
  opacity: 0.7;
}

/* ===== ENHANCED VISUAL EFFECTS ===== */
/* Subtle background patterns for sections */
.section-pattern {
  position: relative;
}

.section-pattern::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, var(--glow-purple-subtle) 1px, transparent 1px),
    radial-gradient(circle at 75% 75%, var(--glow-blue-soft) 1px, transparent 1px);
  background-size: 40px 40px, 60px 60px;
  opacity: 0.3;
  pointer-events: none;
}

/* ===== NEURAL NETWORK STYLES ===== */
.neural-node {
  fill: var(--primary-purple);
  opacity: 0.6;
  transition: all 300ms ease;
}

.neural-path {
  stroke: var(--primary-purple);
  stroke-width: 1;
  opacity: 0.3;
  stroke-dasharray: 5, 5;
  fill: none;
}

/* ===== ANIMATION UTILITIES ===== */
.animate-fade-up {
  animation: fadeUp 0.6s ease-out forwards;
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== THEME-AWARE ANIMATIONS ===== */
@media (prefers-reduced-motion: no-preference) {
  .theme-aware-animation {
    animation-duration: 0.3s;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

@media (prefers-reduced-motion: reduce) {
  .theme-aware-animation {
    animation: none;
  }

  * {
    transition-duration: 0.01ms !important;
  }
}

/* ===== THEME TRANSITION ENHANCEMENTS ===== */
.theme-transition-fast {
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-transition-slow {
  transition: all 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */
@media (max-width: 768px) {
  .display-large {
    font-size: 3rem; /* 48px */
    line-height: 1.1;
  }

  .display-medium {
    font-size: 2.25rem; /* 36px */
    line-height: 1.2;
  }

  .display-small {
    font-size: 1.75rem; /* 28px */
    line-height: 1.3;
  }

  .headline-large {
    font-size: 1.5rem; /* 24px */
    line-height: 1.4;
  }

  .body-large {
    font-size: 1rem; /* 16px */
    line-height: 1.6;
  }
}

/* ===== RESPONSIVE SPACING ===== */
@media (max-width: 768px) {
  .py-24 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  .py-16 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .mb-16 {
    margin-bottom: 3rem !important;
  }

  .mb-10 {
    margin-bottom: 2rem !important;
  }
}

/* ===== RESPONSIVE CARDS ===== */
@media (max-width: 768px) {
  .card {
    padding: 1.5rem;
  }

  .card:hover {
    transform: translateY(-2px);
  }
}

/* ===== RESPONSIVE NAVIGATION ===== */
@media (max-width: 768px) {
  .hero-section {
    min-height: 80vh;
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .hero-section .max-w-4xl {
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
/* GPU acceleration for animations */
.animate-fade-up,
.animate-fade-in,
.animate-scale-in,
.card:hover,
.btn-primary:hover,
.neural-node,
.neural-path {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* Optimize repaints */
.bg-grid,
.neural-network,
.floating-particles {
  contain: layout style paint;
}

/* Reduce layout thrashing */
.hero-section,
.section {
  contain: layout;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
/* GPU acceleration for animations */
.animate-fade-up,
.animate-fade-in,
.animate-scale-in,
.card:hover,
.btn-primary:hover,
.neural-node,
.neural-path {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* Optimize repaints */
.bg-grid,
.neural-network,
.floating-particles {
  contain: layout style paint;
}

/* Reduce layout thrashing */
.hero-section,
.section {
  contain: layout;
}

/* Critical CSS inlining hint */
.above-fold {
  contain: layout style paint;
}

/* Lazy loading optimization */
.lazy-load {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
/* Focus styles */
.focus-visible:focus-visible,
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #333333;
    --border-color: #000000;
    --primary-purple: #4A0E4E;
  }

  [data-theme="dark"] {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-color: #ffffff;
    --primary-purple: #BB86FC;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip to content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-purple);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Error states */
.error-state {
  border-color: #ef4444;
  background-color: rgba(239, 68, 68, 0.05);
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Success states */
.success-state {
  border-color: #10b981;
  background-color: rgba(16, 185, 129, 0.05);
}

.success-message {
  color: #10b981;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
