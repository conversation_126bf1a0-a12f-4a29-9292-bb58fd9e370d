### Epic 4: Advanced Interactivity & Dynamic Elements

*   **Story 4.1: Global Scroll-Triggered Content Reveals**
    *   **Status:** Not Started
    *   **Story:** As a user, I want all major content sections across the website to animate into view as I scroll, so that the website feels dynamic, engaging, and provides a premium browsing experience.
    *   **Acceptance Criteria:**
        1.  All major content sections (e.g., "Our Story," service cards, key benefits) animate into view as they enter the viewport.
        2.  Animations are smooth, varied (e.g., slide-in, fade-up, subtle scale-up), and implemented using GSAP's ScrollTrigger.
        3.  Animations perform well across devices and do not cause any visual jank.
    *   **Tasks:**
        *   [ ] Integrate GSAP ScrollTrigger into the project.
        *   [ ] Apply scroll-triggered animations to major content sections on Home, About, Services, OfImpact, and OfLogic pages.
        *   [ ] Experiment with different animation types (slide-in, fade-up, scale-up) for variety.
        *   [ ] Optimize animations for performance across various devices.
    *   **Dev Notes:**
    *   **Testing:**
    *   **QA Results:**