import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import ThemeProvider from "../components/providers/theme-provider";
import Navigation from "../components/ui/navigation";
import { BackgroundGrid } from "../components/ui/background-grid";
import { Footer } from "../components/ui/footer";

// Inter font configuration for premium typography
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
  preload: true,
});

// Enhanced metadata for ofstartup.ai
export const metadata: Metadata = {
  title: {
    default: "ofstartup.ai - AI-Powered Innovation Seamlessly Integrated",
    template: "%s | ofstartup.ai",
  },
  description: "Transform your business with cutting-edge AI solutions from ofstartup.ai. Founded by Marshal <PERSON>, we deliver OfImpact business intelligence, OfLogic neuromarketing, and custom AI development with 5+ years of excellence.",
  keywords: [
    "AI solutions",
    "artificial intelligence",
    "business automation",
    "data analytics", 
    "machine learning",
    "ofstartup",
    "ofimpact",
    "oflogic",
    "innovation",
    "technology consulting"
  ],
  authors: [{ name: "Ofstartup.ai Team" }],
  creator: "Ofstartup.ai",
  publisher: "Ofstartup.ai",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://ofstartup.ai",
    siteName: "ofstartup.ai",
    title: "ofstartup.ai - AI-Powered Innovation Seamlessly Integrated",
    description: "Transform your business with cutting-edge AI solutions. Founded by Marshal Tudu & Zunaid Ahaal. Discover OfImpact and OfLogic for comprehensive business intelligence and automation.",
    images: [
      {
        url: "/api/og?title=ofstartup.ai%20-%20AI-Powered%20Innovation&description=Transform%20your%20business%20with%20cutting-edge%20AI%20solutions&page=home",
        width: 1200,
        height: 630,
        alt: "ofstartup.ai - AI Innovation Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "ofstartup.ai - AI-Powered Innovation",
    description: "Transform your business with cutting-edge AI solutions including OfImpact and OfLogic. Founded by Marshal Tudu & Zunaid Ahaal.",
    creator: "@ofstartup",
    images: ["/api/og?title=ofstartup.ai%20-%20AI-Powered%20Innovation&description=Transform%20your%20business%20with%20cutting-edge%20AI%20solutions&page=home"],
  },
  icons: {
    icon: [
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
    ],
  },
  manifest: "/site.webmanifest",
};

// Viewport configuration
export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        
        <ThemeProvider
          defaultTheme="system"
          storageKey="ofstartup-theme"
        >
          <BackgroundGrid animated />
          <Navigation />
          <main id="main-content" className="relative">
            {children}
          </main>
          <Footer />
        </ThemeProvider>
      </body>
    </html>
  );
}
