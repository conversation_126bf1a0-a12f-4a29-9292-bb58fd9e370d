### Epic 1: Foundational Setup & Core Structure

*   **Story 1.2: Global Layout, Styling & Responsive Foundation**
    *   **Status:** Not Started
    *   **Story:** As a user, I want the website to have a consistent global layout, basic styling, and responsive behavior across all devices, so that it provides a uniform and accessible viewing experience from the start.
    *   **Acceptance Criteria:**
        1.  A global CSS file is established with basic resets and the primary font imports (Inter or Plus Jakarta Sans).
        2.  A root layout component (`app/layout.tsx`) is created to define the overall page structure and apply global styles.
        3.  The layout is inherently responsive, adapting gracefully to mobile, tablet, and desktop screen sizes.
        4.  The chosen typography is correctly applied as the global default.
        5.  The primary brand color (purple) is defined and accessible via Tailwind CSS for consistent application.
        6.  The main page (`app/page.tsx`) is a minimal, functional component, ready to receive content.
    *   **Tasks:**
        *   [x] Create/update `app/globals.css` with basic CSS resets and font imports.
        *   [x] Create `app/layout.tsx` to define the root layout.
        *   [x] Implement responsive design principles in `layout.tsx` using Tailwind CSS breakpoints.
        *   [x] Configure Tailwind CSS to use Inter or Plus Jakarta Sans as the default font.
        *   [x] Define the primary purple color in `tailwind.config.ts`.
        *   [x] Ensure `app/page.tsx` is a minimal, functional component.
    *   **Dev Notes:**
    *   **Testing:**
    *   **QA Results:**