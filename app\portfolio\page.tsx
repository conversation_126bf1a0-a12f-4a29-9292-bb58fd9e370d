import React from 'react';
import {
  Hospital,
  DollarSign,
  ShoppingCart,
  BookOpen,
  Factory,
  Laptop,
  Zap,
  Car,
  Tv,
  Sprout
} from 'lucide-react';
import { PortfolioGridPaginated } from '../../components/ui/portfolio-grid-paginated';

export const metadata = {
  title: 'Portfolio - Our Project Success Stories | ofstartup.ai',
  description: 'Explore our comprehensive portfolio of 100+ successful AI projects across healthcare, finance, e-commerce, and more. See how ofstartup.ai has transformed businesses with cutting-edge AI solutions since 2020.',
  keywords: [
    'AI portfolio',
    'project success stories',
    'AI case studies',
    'business transformation',
    'AI implementation',
    'machine learning projects',
    'enterprise AI solutions',
    'AI consulting results',
    'technology innovation',
    'digital transformation'
  ],
  openGraph: {
    title: 'Portfolio - AI Project Success Stories | ofstartup.ai',
    description: 'Discover 100+ successful AI projects across industries. See how we transform businesses with innovative AI solutions.',
    type: 'website',
    locale: 'en_US',
    url: 'https://ofstartupglobal.com/portfolio',
    siteName: 'ofstartup.ai',
    images: [
      {
        url: '/api/og?title=Portfolio%20-%20AI%20Project%20Success%20Stories&description=Discover%20100%2B%20successful%20AI%20projects%20across%20industries&page=portfolio',
        width: 1200,
        height: 630,
        alt: 'ofstartup.ai Portfolio - AI Success Stories',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Portfolio - AI Project Success Stories | ofstartup.ai',
    description: 'Discover 100+ successful AI projects across industries. See how we transform businesses with innovative AI solutions.',
    images: ['/api/og?title=Portfolio%20-%20AI%20Project%20Success%20Stories&description=Discover%20100%2B%20successful%20AI%20projects%20across%20industries&page=portfolio'],
  },
  alternates: {
    canonical: 'https://ofstartupglobal.com/portfolio',
  },
};

export default function PortfolioPage() {

  return (
    <>
      {/* Portfolio Stats */}
      <section className="py-36 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Our Impact in Numbers
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Measurable results from our web development projects across diverse industries.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">75+</div>
              <div className="body-regular text-secondary">Projects Completed</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">60+</div>
              <div className="body-regular text-secondary">Happy Clients</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">30%</div>
              <div className="body-regular text-secondary">Avg Time Saved</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">5</div>
              <div className="body-regular text-secondary">Years of Growth</div>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Featured Projects
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Discover how our web development solutions have transformed businesses across various industries.
            </p>
          </div>

          <PortfolioGridPaginated initialProjectsPerPage={12} />
        </div>
      </section>

      {/* Industries Section */}
      <section className="py-24 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Industries We Serve
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Our web development expertise spans across multiple industries, delivering tailored solutions for unique business challenges.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
            {[
              { name: "Healthcare", icon: <Hospital className="w-8 h-8" /> },
              { name: "Finance", icon: <DollarSign className="w-8 h-8" /> },
              { name: "E-commerce", icon: <ShoppingCart className="w-8 h-8" /> },
              { name: "Education", icon: <BookOpen className="w-8 h-8" /> },
              { name: "Manufacturing", icon: <Factory className="w-8 h-8" /> },
              { name: "Technology", icon: <Laptop className="w-8 h-8" /> },
              { name: "Energy", icon: <Zap className="w-8 h-8" /> },
              { name: "Transportation", icon: <Car className="w-8 h-8" /> },
              { name: "Media", icon: <Tv className="w-8 h-8" /> },
              { name: "Agriculture", icon: <Sprout className="w-8 h-8" /> }
            ].map((industry, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 mx-auto mb-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center text-purple-600 dark:text-purple-400 border border-purple-500/20 hover:border-purple-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/20">
                  {industry.icon}
                </div>
                <p className="body-regular text-primary font-medium">{industry.name}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Our Development Process
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              A proven methodology that ensures successful project delivery and measurable business impact.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Discovery",
                description: "Deep dive into your business requirements and technical needs"
              },
              {
                step: "02",
                title: "Design",
                description: "Create user-centered designs and technical architecture"
              },
              {
                step: "03",
                title: "Development",
                description: "Build robust applications with modern technologies and best practices"
              },
              {
                step: "04",
                title: "Deployment",
                description: "Launch your solution with ongoing support and maintenance"
              }
            ].map((process, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-purple-600 rounded-full flex items-center justify-center">
                  <span className="headline-large font-bold text-white">
                    {process.step}
                  </span>
                </div>
                <h3 className="headline-large text-primary mb-4">{process.title}</h3>
                <p className="body-regular text-secondary">{process.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-purple-600 via-black to-purple-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="display-medium mb-6">
            Ready to Join Our Success Stories?
          </h2>
          <p className="headline-medium mb-10 opacity-90">
            Let&apos;s discuss how modern web development can transform your business and create your own success story.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="btn-primary bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 body-large font-semibold rounded-lg"
            >
              Start Your Project
            </a>
            <a
              href="/services"
              className="btn-primary bg-transparent border-2 border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4 body-large font-semibold rounded-lg"
            >
              View Our Services
            </a>
          </div>
        </div>
      </section>
    </>
  );
}
