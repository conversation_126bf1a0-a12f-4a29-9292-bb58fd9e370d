'use client';

import React, { useEffect, useState } from 'react';
import { useTheme } from '../providers/theme-provider';

interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

// Premium Theme Toggle Component - 40x24px Specification
export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'md'
}) => {
  const [mounted, setMounted] = useState(false);

  // Safely get theme context
  let resolvedTheme = 'light';
  let toggleTheme = () => {};

  try {
    const themeContext = useTheme();
    resolvedTheme = themeContext.resolvedTheme || 'light';
    toggleTheme = themeContext.toggleTheme;
  } catch {
    // Theme provider not available, use defaults
  }

  const isDark = resolvedTheme === 'dark';

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div
        className={`
          relative inline-flex items-center justify-center
          opacity-50 cursor-not-allowed
          ${className}
        `}
        style={{
          width: size === 'sm' ? '32px' : size === 'lg' ? '48px' : '40px',
          height: size === 'sm' ? '20px' : size === 'lg' ? '28px' : '24px',
        }}
        aria-label="Loading theme toggle"
      >
        <div
          className="absolute inset-0 rounded-full bg-gray-200 animate-pulse"
        />
      </div>
    );
  }

  // Size configurations
  const sizeConfig = {
    sm: {
      width: '32px',
      height: '20px',
      handleSize: '16px',
      translateX: '12px',
    },
    md: {
      width: '40px',
      height: '24px',
      handleSize: '20px',
      translateX: '16px',
    },
    lg: {
      width: '48px',
      height: '28px',
      handleSize: '24px',
      translateX: '20px',
    },
  };

  const config = sizeConfig[size];

  return (
    <button
      onClick={toggleTheme}
      className={`
        theme-toggle
        relative inline-flex items-center justify-center
        transition-all duration-300 ease-out
        focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
        focus:ring-offset-transparent
        hover:scale-105 active:scale-95
        ${className}
      `}
      style={{
        width: config.width,
        height: config.height,
      }}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} theme`}
      role="switch"
      aria-checked={isDark}
    >
      {/* Toggle Track */}
      <div
        className={`
          absolute inset-0 rounded-full transition-all duration-300 ease-out
          ${isDark
            ? 'bg-gradient-to-r from-purple-600 to-purple-700 shadow-lg'
            : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 shadow-lg'
          }
        `}
        style={{
          boxShadow: isDark 
            ? '0 4px 12px rgba(138, 43, 226, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.1)' 
            : '0 2px 6px rgba(0, 0, 0, 0.1), inset 0 1px 2px rgba(0, 0, 0, 0.05)',
        }}
      />
      
      {/* Toggle Handle */}
      <div
        className={`
          absolute top-1/2 -translate-y-1/2 rounded-full transition-all duration-300 ease-out
          flex items-center justify-center
          ${isDark 
            ? 'bg-white shadow-lg translate-x-4' 
            : 'bg-white shadow-md translate-x-0.5'
          }
        `}
        style={{
          width: config.handleSize,
          height: config.handleSize,
          transform: `translateY(-50%) translateX(${isDark ? config.translateX : '2px'})`,
          boxShadow: isDark
            ? '0 4px 8px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1)'
            : '0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05)',
        }}
      >
        {/* Icon inside handle */}
        <div className="w-3 h-3 flex items-center justify-center">
          {isDark ? (
            // Moon icon
            <svg
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="none"
              className="text-purple-600"
            >
              <path
                d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
                fill="currentColor"
              />
            </svg>
          ) : (
            // Sun icon
            <svg
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="none"
              className="text-yellow-500"
            >
              <circle cx="12" cy="12" r="4" fill="currentColor" />
              <path
                d="m12 2 0 2m0 16 0 2M4.93 4.93l1.41 1.41m11.32 11.32 1.41 1.41M2 12l2 0m16 0 2 0M4.93 19.07l1.41-1.41M17.66 6.34l1.41-1.41"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
              />
            </svg>
          )}
        </div>
      </div>
      
      {/* Subtle glow effect for dark mode */}
      {isDark && (
        <div
          className="absolute inset-0 rounded-full opacity-50 blur-sm"
          style={{
            background: 'linear-gradient(45deg, rgba(138, 43, 226, 0.3), rgba(147, 112, 219, 0.3))',
            zIndex: -1,
          }}
        />
      )}
    </button>
  );
};

// Alternative minimal toggle (no icons)
export const MinimalThemeToggle: React.FC<ThemeToggleProps> = ({ 
  className = '',
  size = 'md' 
}) => {
  const [mounted, setMounted] = useState(false);
  const { resolvedTheme, toggleTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';
  
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    const config = {
      sm: { width: '32px', height: '20px' },
      md: { width: '40px', height: '24px' },
      lg: { width: '48px', height: '28px' },
    }[size];

    return (
      <div
        className={`
          relative inline-flex items-center justify-center
          opacity-50 cursor-not-allowed
          ${className}
        `}
        style={{ width: config.width, height: config.height }}
        aria-label="Loading theme toggle"
      >
        <div className="absolute inset-0 rounded-full bg-gray-200 animate-pulse" />
      </div>
    );
  }
  
  const config = {
    sm: { width: '32px', height: '20px', handleSize: '16px', translateX: '12px' },
    md: { width: '40px', height: '24px', handleSize: '20px', translateX: '16px' },
    lg: { width: '48px', height: '28px', handleSize: '24px', translateX: '20px' },
  }[size];

  return (
    <button
      onClick={toggleTheme}
      className={`
        relative inline-flex items-center justify-center
        transition-all duration-300 cubic-bezier(0.4, 0, 0.2, 1)
        focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
        hover:scale-105 active:scale-95
        ${className}
      `}
      style={{ width: config.width, height: config.height }}
      aria-label={`Switch to ${isDark ? 'light' : 'dark'} theme`}
    >
      <div
        className={`
          absolute inset-0 rounded-full transition-all duration-300 cubic-bezier(0.4, 0, 0.2, 1)
          ${isDark ? 'bg-purple-600' : 'bg-purple-500'}
        `}
      />
      <div
        className="absolute top-1/2 bg-white rounded-full shadow-lg transition-all duration-300 cubic-bezier(0.4, 0, 0.2, 1)"
        style={{
          width: config.handleSize,
          height: config.handleSize,
          transform: `translateY(-50%) translateX(${isDark ? config.translateX : '2px'})`,
        }}
      />
    </button>
  );
};

export default ThemeToggle;