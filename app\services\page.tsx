import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { ServiceCardGrid } from '../../components/ui/service-card';
import { StatsCard } from '../../components/ui/custom-card';

export const metadata = {
  title: 'AI Services - Comprehensive AI Solutions | ofstartup.ai',
  description: 'Discover our comprehensive AI services: OfImpact business intelligence, OfLogic neuromarketing, custom AI development, and expert consulting. Transform your business with cutting-edge AI solutions.',
  keywords: [
    'AI services',
    'business intelligence',
    'neuromarketing',
    'custom AI development',
    'AI consulting',
    'machine learning',
    'data analytics',
    'artificial intelligence solutions',
    'enterprise AI',
    'AI transformation'
  ],
  openGraph: {
    title: 'AI Services - Comprehensive Solutions | ofstartup.ai',
    description: 'Transform your business with our AI services: OfImpact, OfLogic, custom development, and expert consulting.',
    type: 'website',
    locale: 'en_US',
    url: 'https://ofstartupglobal.com/services',
    siteName: 'ofstartup.ai',
    images: [
      {
        url: '/api/og?title=AI%20Services%20-%20Comprehensive%20Solutions&description=Transform%20your%20business%20with%20our%20AI%20services%3A%20OfImpact%2C%20OfLogic%2C%20custom%20development&page=services',
        width: 1200,
        height: 630,
        alt: 'ofstartup.ai AI Services',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI Services - Comprehensive Solutions | ofstartup.ai',
    description: 'Transform your business with our AI services: OfImpact, OfLogic, custom development, and expert consulting.',
    images: ['/api/og?title=AI%20Services%20-%20Comprehensive%20Solutions&description=Transform%20your%20business%20with%20our%20AI%20services%3A%20OfImpact%2C%20OfLogic%2C%20custom%20development&page=services'],
  },
  alternates: {
    canonical: 'https://ofstartupglobal.com/services',
  },
};

export default function ServicesPage() {
  const services = [
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      title: "OfImpact - Business Intelligence",
      description: "Comprehensive analytics platform that transforms your data into actionable insights with real-time dashboards and predictive modeling.",
      ctaText: "Explore OfImpact",
      ctaHref: "/ofimpact",
      featured: true
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
      title: "OfLogic - Neuromarketing",
      description: "Advanced consumer behavior analysis using neuroscience and AI to optimize marketing strategies and increase conversions.",
      ctaText: "Discover OfLogic",
      ctaHref: "/oflogic",
      featured: true
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      title: "Custom AI Development",
      description: "Tailored AI solutions designed specifically for your unique business requirements and challenges.",
      ctaText: "Get Custom Solution",
      ctaHref: "/contact",
      featured: true
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      title: "AI Consulting",
      description: "Strategic guidance and expertise to help you navigate your AI transformation journey.",
      ctaText: "Book Consultation",
      ctaHref: "/contact",
      featured: true
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      title: "Machine Learning Models",
      description: "Custom ML models for prediction, classification, and optimization tailored to your data.",
      ctaText: "Learn More",
      ctaHref: "/contact",
      featured: true
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
      title: "AI Integration",
      description: "Seamless integration of AI capabilities into your existing systems and workflows.",
      ctaText: "Get Started",
      ctaHref: "/contact",
      featured: true
    }
  ];

  return (
    <>
      {/* Services Grid */}
      <section id="services" className="py-24 section-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Our AI Services
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Discover our comprehensive range of AI solutions designed to address your specific business challenges.
            </p>
          </div>

          <ServiceCardGrid services={services} />
        </div>
      </section>

      {/* Service Metrics */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Service Performance Metrics
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Real data from our AI implementations showing measurable business impact.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <StatsCard
              title="Implementation Speed"
              value="60%"
              description="Faster deployment than industry average"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              trend={{ value: 25, isPositive: true }}
            />
            <StatsCard
              title="Cost Reduction"
              value="45%"
              description="Average operational cost savings"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              }
              trend={{ value: 18, isPositive: true }}
            />
            <StatsCard
              title="Accuracy Rate"
              value="97.8%"
              description="AI model performance accuracy"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
              trend={{ value: 12, isPositive: true }}
            />
            <StatsCard
              title="Client Retention"
              value="96%"
              description="Long-term client satisfaction rate"
              icon={
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              }
              trend={{ value: 8, isPositive: true }}
            />
          </div>
        </div>
      </section>

      {/* Service Impact Analysis - Enhanced Design */}
      <section className="py-24 section-bg-secondary relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 20% 50%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
                             radial-gradient(circle at 80% 20%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
                             radial-gradient(circle at 40% 80%, rgba(147, 51, 234, 0.1) 0%, transparent 50%)`
          }} />
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Service Impact Analysis
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              Comprehensive analysis of business impact and ROI across our AI services portfolio.
            </p>
          </div>

          {/* Enhanced Chart Container */}
          <div className="bg-white/90 dark:bg-black/90 backdrop-blur-xl rounded-2xl p-8 shadow-2xl border border-purple-500/20">
            {/* Outer glow */}
            <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/20 via-blue-600/20 to-purple-600/20 rounded-2xl blur-sm -z-10"></div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">

              {/* Enhanced Bar Chart */}
              <div className="space-y-6">
                <h3 className="headline-large text-primary mb-8 text-center">ROI by Service Type</h3>
                <div className="space-y-6">
                  {[
                    { service: "OfImpact", value: 340, color: "bg-gradient-to-r from-purple-600 to-purple-700", icon: <BarChart3 className="w-6 h-6" /> },
                    { service: "OfLogic", value: 280, color: "bg-gradient-to-r from-blue-600 to-blue-700", icon: <Brain className="w-6 h-6" /> },
                    { service: "Custom AI", value: 220, color: "bg-gradient-to-r from-green-600 to-green-700", icon: <Settings className="w-6 h-6" /> },
                    { service: "ML Models", value: 190, color: "bg-gradient-to-r from-orange-600 to-orange-700", icon: <Bot className="w-6 h-6" /> },
                    { service: "AI Integration", value: 150, color: "bg-gradient-to-r from-red-600 to-red-700", icon: <Link className="w-6 h-6" /> }
                  ].map((item, index) => (
                    <div key={index} className="group">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <div className="text-purple-600 dark:text-purple-400">{item.icon}</div>
                          <span className="body-large font-medium text-primary">{item.service}</span>
                        </div>
                        <span className="headline-medium font-bold text-primary">{item.value}%</span>
                      </div>
                      <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-4 relative overflow-hidden">
                        <div
                          className={`${item.color} h-4 rounded-full transition-all duration-1000 ease-out group-hover:scale-105`}
                          style={{ width: `${(item.value / 340) * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Enhanced Pie Chart */}
              <div className="space-y-6">
                <h3 className="headline-large text-primary mb-8 text-center">Service Distribution</h3>
                <div className="relative w-80 h-80 mx-auto">
                  {/* Enhanced Pie chart visualization */}
                  <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                    <circle
                      cx="50"
                      cy="50"
                      r="35"
                      fill="none"
                      stroke="url(#gradient1)"
                      strokeWidth="15"
                      strokeDasharray="45 55"
                      strokeDashoffset="0"
                      className="drop-shadow-lg"
                    />
                    <circle
                      cx="50"
                      cy="50"
                      r="35"
                      fill="none"
                      stroke="url(#gradient2)"
                      strokeWidth="15"
                      strokeDasharray="30 70"
                      strokeDashoffset="-45"
                      className="drop-shadow-lg"
                    />
                    <circle
                      cx="50"
                      cy="50"
                      r="35"
                      fill="none"
                      stroke="url(#gradient3)"
                      strokeWidth="15"
                      strokeDasharray="25 75"
                      strokeDashoffset="-75"
                      className="drop-shadow-lg"
                    />
                    <defs>
                      <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#8B5CF6" />
                        <stop offset="100%" stopColor="#A855F7" />
                      </linearGradient>
                      <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#3B82F6" />
                        <stop offset="100%" stopColor="#60A5FA" />
                      </linearGradient>
                      <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#10B981" />
                        <stop offset="100%" stopColor="#34D399" />
                      </linearGradient>
                    </defs>
                  </svg>

                  {/* Enhanced Legend */}
                  <div className="absolute -bottom-12 left-0 right-0">
                    <div className="flex justify-center space-x-6">
                      <div className="flex items-center space-x-2 bg-white/80 dark:bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2">
                        <div className="w-4 h-4 bg-gradient-to-r from-purple-600 to-purple-700 rounded-full"></div>
                        <span className="body-small font-medium text-primary">Business Intelligence (45%)</span>
                      </div>
                      <div className="flex items-center space-x-2 bg-white/80 dark:bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2">
                        <div className="w-4 h-4 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full"></div>
                        <span className="body-small font-medium text-primary">Neuromarketing (30%)</span>
                      </div>
                      <div className="flex items-center space-x-2 bg-white/80 dark:bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2">
                        <div className="w-4 h-4 bg-gradient-to-r from-green-600 to-green-700 rounded-full"></div>
                        <span className="body-small font-medium text-primary">Custom Solutions (25%)</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Service Process */}
      <section className="py-24 section-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="display-medium text-primary mb-6">
              Our Service Methodology
            </h2>
            <p className="headline-medium text-secondary max-w-3xl mx-auto">
              A proven approach that ensures successful AI implementation and measurable business results.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {[
              {
                step: "01",
                title: "Assessment",
                description: "Comprehensive analysis of your current systems and AI readiness"
              },
              {
                step: "02",
                title: "Strategy",
                description: "Custom AI strategy aligned with your business objectives"
              },
              {
                step: "03",
                title: "Development",
                description: "Agile development with continuous testing and validation"
              },
              {
                step: "04",
                title: "Integration",
                description: "Seamless integration with minimal disruption to operations"
              },
              {
                step: "05",
                title: "Optimization",
                description: "Ongoing monitoring and optimization for peak performance"
              }
            ].map((process, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-purple-600 rounded-full flex items-center justify-center">
                  <span className="headline-medium font-bold text-white">
                    {process.step}
                  </span>
                </div>
                <h3 className="headline-large text-primary mb-4">{process.title}</h3>
                <p className="body-regular text-secondary">{process.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-purple-600 via-black to-purple-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="display-medium mb-6">
            Ready to Transform Your Business?
          </h2>
          <p className="headline-medium mb-10 opacity-90">
            Let&apos;s discuss which AI services are right for your business and create a custom solution.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="btn-primary bg-white text-purple-600 hover:bg-gray-100 px-8 py-4 body-large font-semibold rounded-lg"
            >
              Schedule Consultation
            </a>
            <a
              href="/portfolio"
              className="btn-primary bg-transparent border-2 border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4 body-large font-semibold rounded-lg"
            >
              View Case Studies
            </a>
          </div>
        </div>
      </section>
    </>
  );
}
