import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Meet Our Founders - Marshal <PERSON><PERSON> & <PERSON><PERSON><PERSON> | ofstartup.ai',
  description: 'Meet the visionary founders of ofstartup.ai: <PERSON> (Technical Lead & Project Management) and <PERSON><PERSON><PERSON> (Business & Marketing Operations). 5+ years of AI innovation and 100+ successful projects.',
  keywords: [
    'Marshal <PERSON>',
    '<PERSON><PERSON><PERSON>',
    'ofstartup founders',
    'AI company founders',
    'technology leadership',
    'AI innovation',
    'business leadership',
    'startup founders',
    'AI entrepreneurs',
    'technology visionaries'
  ],
  openGraph: {
    title: 'Meet Our Founders - Marshal <PERSON> & <PERSON> | ofstartup.ai',
    description: 'Meet the visionary founders behind ofstartup.ai. 5+ years of AI innovation and 100+ successful projects.',
    type: 'website',
    locale: 'en_US',
    url: 'https://ofstartupglobal.com/founders',
    siteName: 'ofstartup.ai',
    images: [
      {
        url: '/api/og?title=Meet%20Our%20Founders%20-%20Marshal%20Tudu%20%26%20Zunaid%20Ahaal&description=Meet%20the%20visionary%20founders%20behind%20ofstartup.ai&page=founders',
        width: 1200,
        height: 630,
        alt: 'ofstartup.ai Founders - Marshal <PERSON>du & <PERSON>unaid Ahaal',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Meet Our Founders - Marshal Tudu & Zunaid Ahaal | ofstartup.ai',
    description: 'Meet the visionary founders behind ofstartup.ai. 5+ years of AI innovation and 100+ successful projects.',
    images: ['/api/og?title=Meet%20Our%20Founders%20-%20Marshal%20Tudu%20%26%20Zunaid%20Ahaal&description=Meet%20the%20visionary%20founders%20behind%20ofstartup.ai&page=founders'],
  },
  alternates: {
    canonical: 'https://ofstartupglobal.com/founders',
  },
};

export default function FoundersLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
