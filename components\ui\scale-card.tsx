'use client';

import React from 'react';

interface ScaleCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'featured' | 'glow';
  size?: 'sm' | 'md' | 'lg';
  interactive?: boolean;
  style?: React.CSSProperties;
}

interface ScaleCardComponent extends React.FC<ScaleCardProps> {
  Header: React.FC<ScaleCardHeaderProps>;
  Content: React.FC<ScaleCardContentProps>;
  Footer: React.FC<ScaleCardFooterProps>;
}

export const ScaleCard: ScaleCardComponent = ({
  children,
  className = '',
  variant = 'default',
  size = 'md',
  interactive = true,
  style
}) => {
  const sizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const variantClasses = {
    default: '',
    featured: 'card-glow',
    glow: 'card-glow'
  };

  const baseClasses = `
    card
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${interactive ? 'cursor-pointer' : ''}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <div
      className={baseClasses}
      style={style}
    >
      {children}
    </div>
  );
};

interface ScaleCardHeaderProps {
  icon?: React.ReactNode;
  title: string;
  subtitle?: string;
  className?: string;
}

export const ScaleCardHeader: React.FC<ScaleCardHeaderProps> = ({
  icon,
  title,
  subtitle,
  className = ''
}) => {
  return (
    <div className={`flex items-start space-x-4 mb-6 ${className}`}>
      {icon && (
        <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center flex-shrink-0">
          {icon}
        </div>
      )}
      <div className="flex-1">
        <h3 className="headline-large text-primary mb-1">{title}</h3>
        {subtitle && (
          <p className="body-regular text-secondary">{subtitle}</p>
        )}
      </div>
    </div>
  );
};

interface ScaleCardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const ScaleCardContent: React.FC<ScaleCardContentProps> = ({
  children,
  className = ''
}) => {
  return (
    <div className={`text-secondary ${className}`}>
      {children}
    </div>
  );
};

interface ScaleCardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const ScaleCardFooter: React.FC<ScaleCardFooterProps> = ({
  children,
  className = ''
}) => {
  return (
    <div className={`mt-6 pt-4 border-t border-default ${className}`}>
      {children}
    </div>
  );
};

// Compound component pattern
ScaleCard.Header = ScaleCardHeader as React.FC<ScaleCardHeaderProps>;
ScaleCard.Content = ScaleCardContent as React.FC<ScaleCardContentProps>;
ScaleCard.Footer = ScaleCardFooter as React.FC<ScaleCardFooterProps>;

export default ScaleCard;
