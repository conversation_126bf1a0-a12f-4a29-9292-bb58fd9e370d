'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { ThemeToggle } from './theme-toggle';

interface NavigationProps {
  className?: string;
}

interface NavItem {
  href: string;
  label: string;
  isActive?: boolean;
}

export const Navigation: React.FC<NavigationProps> = ({ className = '' }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  // Navigation items
  const navItems: NavItem[] = [
    { href: '/', label: 'Home' },
    { href: '/services', label: 'Services' },
    { href: '/ofimpact', label: 'OfImpact' },
    { href: '/oflogic', label: 'OfLogic' },
    { href: '/portfolio', label: 'Portfolio' },
  ];

  // Add active state to nav items
  const navItemsWithActive = navItems.map(item => ({
    ...item,
    isActive: pathname === item.href
  }));

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsScrolled(scrollPosition > 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [pathname]);

  return (
    <>
      {/* Navigation Bar */}
      <nav
        className={`
          fixed top-0 left-0 right-0 z-50
          transition-all duration-200 ease-out
          ${isScrolled 
            ? 'h-14 backdrop-blur-md bg-white/80 dark:bg-gray-900/80 shadow-lg' 
            : 'h-18 bg-transparent'
          }
          ${className}
        `}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
          <div className="flex items-center justify-between h-full">
            
            {/* Logo Area */}
            <Link
              href="/"
              className={`
                flex items-center space-x-1 transition-all duration-200 ease-out
                ${isScrolled ? 'scale-90' : 'scale-100'}
                hover:opacity-80
              `}
              aria-label="Ofstartup.ai home page"
            >
              <Image
                src="/logo.webp"
                alt="ofstartup.ai Logo"
                width={40}
                height={40}
                className="w-15 h-15 object-contain"
                priority
              />
              <span className="headline-medium font-semibold text-primary">
                ofstartup.ai
              </span>
            </Link>

            {/* Desktop Navigation Links */}
            <div className="hidden md:flex items-center space-x-8">
              {navItemsWithActive.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`
                    relative body-regular font-medium transition-all duration-200 ease-out
                    hover:text-purple-600 dark:hover:text-purple-400 hover:scale-105
                    focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-transparent
                    ${item.isActive 
                      ? 'text-purple-600 dark:text-purple-400' 
                      : 'text-secondary hover:text-primary'
                    }
                  `}
                >
                  {item.label}
                  {/* Active indicator */}
                  {item.isActive && (
                    <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-purple-600 dark:bg-purple-400 rounded-full" />
                  )}
                </Link>
              ))}
            </div>

            {/* Theme Toggle & Mobile Menu Button */}
            <div className="flex items-center space-x-4">
              <ThemeToggle size="md" />
              
              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className={`
                  md:hidden relative w-6 h-6 flex flex-col justify-center items-center
                  transition-all duration-300 ease-out
                  focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
                `}
                aria-label="Toggle mobile menu"
                aria-expanded={isMobileMenuOpen}
              >
                <span
                  className={`
                    block w-5 h-0.5 bg-current transition-all duration-300 ease-out
                    ${isMobileMenuOpen ? 'rotate-45 translate-y-0.5' : 'rotate-0 -translate-y-1'}
                  `}
                />
                <span
                  className={`
                    block w-5 h-0.5 bg-current transition-all duration-300 ease-out
                    ${isMobileMenuOpen ? 'opacity-0' : 'opacity-100'}
                  `}
                />
                <span
                  className={`
                    block w-5 h-0.5 bg-current transition-all duration-300 ease-out
                    ${isMobileMenuOpen ? '-rotate-45 -translate-y-0.5' : 'rotate-0 translate-y-1'}
                  `}
                />
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsMobileMenuOpen(false)}
          />
          
          {/* Menu Panel */}
          <div className="fixed top-0 right-0 w-full max-w-sm h-full glass glass-light dark:glass-dark shadow-2xl">
            <div className="flex flex-col h-full pt-20 pb-6 px-6">
              {/* Mobile Navigation Links */}
              <nav className="flex-1">
                <div className="space-y-1">
                  {navItemsWithActive.map((item, index) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={`
                        block px-4 py-3 rounded-lg transition-all duration-200 ease-out
                        display-small font-semibold
                        ${item.isActive 
                          ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' 
                          : 'text-secondary hover:text-primary hover:bg-gray-100 dark:hover:bg-gray-800'
                        }
                      `}
                      style={{
                        animationDelay: `${index * 50}ms`,
                        animation: 'slideDown 0.3s ease-out forwards'
                      }}
                    >
                      {item.label}
                    </Link>
                  ))}
                </div>
              </nav>
              
              {/* Mobile Menu Footer */}
              <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
                <p className="body-small text-tertiary text-center">
                  AI-Powered Innovation
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add slide down animation for mobile menu items */}
      <style jsx>{`
        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </>
  );
};

export default Navigation;
